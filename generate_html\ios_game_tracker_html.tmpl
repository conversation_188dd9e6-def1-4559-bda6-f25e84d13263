<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>__TITLE__</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", sans-serif; margin: 10px; font-size: 15px; }
        details { margin-bottom: 20px; }
        details summary { padding: 10px; background: #f5f5f5; border-radius: 8px; cursor: pointer; margin-bottom: 10px; }
        details summary:hover { background: #e8e8e8; }
        .app-grid { display: flex; flex-wrap: wrap; gap: 16px; padding: 16px; min-height: 100px; }
        .app-grid.list-mode { display: block; }
        .app-grid.icon-mode { display: flex; flex-wrap: wrap; gap: 16px; padding: 16px; }
        .app-item { flex: 0 0 160px; padding: 10px; border: 1px solid #ddd; border-radius: 8px; font-size: 14px; background: #fff; box-shadow: 0 1px 3px rgba(0,0,0,0.1); }
        .app-item.list-mode { display: grid; grid-template-columns: 80px 200px 1fr 1fr 1fr; gap: 20px; align-items: center; margin-bottom: 10px; flex: none; width: auto; }
        .app-item.icon-mode { flex: 0 0 160px; padding: 10px; display: flex; flex-direction: column; gap: 4px; }
        .app-item.icon-mode:hover { transform: translateY(-2px); box-shadow: 0 4px 8px rgba(0,0,0,0.1); }
        .app-item.icon-mode .app-icon { width: 80px; height: 80px; margin: 0 auto 8px; }
        .app-item.icon-mode a.app-name { font-size: 14px; font-weight: bold; margin-bottom: 2px; overflow: hidden; text-overflow: ellipsis; display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical; min-height: 36px; color: #000000; text-decoration: none; }
        .app-item.icon-mode .app-id { font-size: 11px; color: #999; margin-bottom: 2px; }
        .app-item.icon-mode a.developer-name { font-size: 12px; color: #666; text-decoration: none; overflow: hidden; text-overflow: ellipsis; display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical; min-height: 24px; margin-bottom: 4px; }
        .app-item.icon-mode a.developer-name:hover { color: #007AFF; text-decoration: underline; }
        .app-item.icon-mode .app-date { font-size: 12px; color: #666; }
        .app-item.icon-mode .rating { display: flex; align-items: center; gap: 4px; }
        .app-item.icon-mode .game-tags { display: flex; flex-wrap: wrap; gap: 4px; margin-top: 2px; }
        .app-item.icon-mode .game-tag { font-size: 11px; padding: 2px 6px; background: #f5f5f5; border-radius: 4px; color: #666; }
        .app-item.list-mode .app-icon { width: 80px; height: 80px; margin: 0; }
        .app-item.list-mode .app-info { display: flex; flex-direction: column; gap: 4px; }
        .app-item.list-mode .app-id { font-size: 11px; color: #999; margin-bottom: 2px; }
        .app-item.list-mode .app-dates { display: flex; flex-direction: column; gap: 4px; }
        .app-item.list-mode .app-stats { display: flex; flex-direction: column; gap: 4px; }
        .app-item.list-mode .game-tags { margin: 0; }
        .app-item a { font-weight: bold; display: block; margin-bottom: 5px; color: #000000; text-decoration: none; }
        .app-item a:hover { color: #007AFF; }
        .app-date { font-size: 12px; color: #666; }
        @media (max-width: 1200px) {
          .app-item { flex: 0 0 140px; }
          .app-item.icon-mode { flex: 0 0 120px; }
        }
        @media (max-width: 768px) {
          body { padding: 12px; }
          .app-grid { gap: 10px; padding: 12px; }
          .app-item { padding: 10px; }
          .app-item.icon-mode { flex: 0 0 calc(33.33% - 8px); }
          .filter-content { padding: 16px; }
          .tag-filter-grid { grid-template-columns: repeat(auto-fill, minmax(120px, 1fr)); }
          .tag-filter-item { padding: 6px 8px; font-size: 12px; }
        }
        @media (max-width: 480px) {
          .app-grid { gap: 8px; padding: 8px; }
          .app-item { padding: 8px; }
          .app-item.icon-mode { flex: 0 0 calc(42%); }
          .filter-toggle { width: 48px; height: 48px; border-radius: 24px; font-size: 20px; }
        }
        @media (max-width: 360px) {
          .app-item.icon-mode { flex: 0 0 100%; }
        }
        .vendor-header { font-size: 16px; font-weight: bold; margin-top: 20px; }
        .filter-buttons { display: flex; flex-wrap: wrap; gap: 6px; margin-bottom: 10px; }
        .filter-button { padding: 6px 12px; font-size: 14px; border: none; border-radius: 6px; background: #007bff; color: white; cursor: pointer; }
        .filter-button.active { background: #0056b3; }
        h1 { font-size: 24px; margin: 0 0 20px 0; color: #333; }
        .vendor-section { margin-bottom: 16px; background: white; border-radius: 12px; overflow: hidden; box-shadow: 0 1px 3px rgba(0,0,0,0.1); }
        .vendor-header a { color: #333; text-decoration: none; }
        .vendor-header a:hover { color: #007AFF; }
        .vendor-header .game-count { font-size: 14px; color: #666; font-weight: normal; }
        .app-icon { width: 100%; height: auto; aspect-ratio: 1; border-radius: 22%; margin-bottom: 8px; cursor: pointer; max-width: 120px; margin: 0 auto 8px; display: block; }
        .app-icon.has-screenshots { border: 3px solid #34C759; box-shadow: 0 2px 8px rgba(52, 199, 89, 0.2); }
        .filter-toggle { position: fixed; bottom: 20px; right: 20px; width: 56px; height: 56px; border-radius: 28px; background: #007AFF; color: white; border: none; font-size: 24px; box-shadow: 0 2px 8px rgba(0,0,0,0.2); z-index: 100; cursor: pointer; display: flex; align-items: center; justify-content: center; transition: transform 0.2s; }
        .filter-toggle:active { transform: scale(0.95); }
        .filter-panel { display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; overflow: hidden; }
        .filter-panel.active { display: block; }
        .filter-content { position: fixed; bottom: 0; left: 0; width: 100%; max-width: 100%; background: white; border-radius: 20px 20px 0 0; padding: 20px; box-shadow: 0 -2px 10px rgba(0,0,0,0.1); max-height: 85vh; overflow-y: auto; -webkit-overflow-scrolling: touch; box-sizing: border-box; }
        .filter-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; padding-bottom: 15px; border-bottom: 1px solid #eee; position: sticky; top: 0; background: white; z-index: 1001; }
        .filter-title { font-size: 20px; font-weight: 600; color: #333; background: linear-gradient(45deg, #007AFF, #5856D6); -webkit-background-clip: text; -webkit-text-fill-color: transparent; }
        .vendor-section-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px; }
        .vendor-controls { display: flex; align-items: center; gap: 8px; }
        .sort-dropdown { position: relative; display: inline-block; }
        .sort-dropdown-btn { background: #f5f5f5; border: 1px solid #ddd; border-radius: 6px; padding: 6px 12px; cursor: pointer; font-size: 12px; transition: all 0.2s; color: #333; font-weight: 500; }
        .sort-dropdown-btn:hover { background: #e8e8e8; }
        .sort-dropdown-content { display: none; position: absolute; top: 100%; left: 0; background: white; border: 1px solid #ddd; border-radius: 6px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); z-index: 1002; min-width: 120px; }
        .sort-dropdown.active .sort-dropdown-content { display: block; }
        .sort-option { padding: 8px 12px; cursor: pointer; font-size: 13px; transition: background 0.2s; }
        .sort-option:hover { background: #f5f5f5; }
        .sort-option.active { background: #007AFF; color: white; }
        .select-top100-button { padding: 6px 12px; font-size: 12px; border: none; border-radius: 6px; background: #34C759; color: white; cursor: pointer; font-weight: 600; transition: all 0.2s; }
        .select-top100-button:hover { background: #28a745; transform: scale(1.02); }
        .select-top100-button:active { transform: scale(0.98); }
        #select-all-tags:hover { background: #0056b3 !important; }
        #invert-tags:hover { background: #e6850e !important; }
        .vendor-select-dropdown { position: relative; display: inline-block; }
        .vendor-select-btn { background: #34C759; border: none; border-radius: 6px; padding: 6px 12px; cursor: pointer; font-size: 12px; color: white; font-weight: 600; transition: all 0.2s; }
        .vendor-select-btn:hover { background: #28a745; transform: scale(1.02); }
        .vendor-select-btn:active { transform: scale(0.98); }
        .vendor-select-content { display: none; position: absolute; top: 100%; left: 0; background: white; border: 1px solid #ddd; border-radius: 6px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); z-index: 1002; min-width: 120px; }
        .vendor-select-dropdown.active .vendor-select-content { display: block; }
        .vendor-select-option { padding: 8px 12px; cursor: pointer; font-size: 13px; transition: background 0.2s; }
        .vendor-select-option:hover { background: #f5f5f5; }
        .vendor-select-option.active { background: #34C759; color: white; }
        .filter-close { background: none; border: none; font-size: 24px; color: #666; cursor: pointer; padding: 8px; margin: -8px; }
        .filter-section { margin-bottom: 24px; }
        .filter-section-title { font-size: 16px; font-weight: 600; color: #333; margin-bottom: 12px; display: flex; align-items: center; }
        .filter-section-title::before { content: ""; display: inline-block; width: 4px; height: 16px; background: #007AFF; margin-right: 8px; border-radius: 2px; }
        .name-filter-section { margin-bottom: 20px; box-sizing: border-box; width: 100%; }
        .name-filter-input { width: 100%; padding: 12px 16px; border: 2px solid #e8e8e8; border-radius: 12px; font-size: 15px; transition: all 0.3s; background: #f8f8f8; box-sizing: border-box; }
        .name-filter-input:focus { outline: none; border-color: #007AFF; background: white; box-shadow: 0 2px 8px rgba(0,123,255,0.1); }
        .name-filter-input::placeholder { color: #999; }
        .tag-filter-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(140px, 1fr)); gap: 8px; padding: 4px; }
        .tag-filter-item { display: flex; align-items: center; justify-content: center; padding: 8px 12px; border-radius: 8px; background: #f5f5f5; color: #666; font-size: 14px; cursor: pointer; user-select: none; transition: all 0.2s; border: none; text-align: center; min-height: 36px; position: relative; }
        .tag-filter-item input[type="checkbox"] { cursor: pointer; margin: 0; }
        .vendor-display { display: flex; flex-direction: column; align-items: center; gap: 2px; width: 100%; }
        .vendor-header-row { display: flex; align-items: center; justify-content: space-between; width: 100%; margin-bottom: 2px; }
        .vendor-rank { font-size: 10px; color: #999; font-weight: 600; }
        .vendor-name { font-weight: 600; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; max-width: 110px; }
        .vendor-total-rating { font-size: 11px; color: #888; font-weight: 400; }
        .vendor-success-rate { font-size: 11px; color: #888; }

        .tag-filter-item:active { transform: scale(0.98); }
        .tag-filter-item.selected { background: #007AFF; color: white; }
        .tag-filter-item.selected .vendor-rank { color: #e6f3ff; }
        .tag-filter-item.selected .vendor-total-rating { color: #cce7ff; }
        .tag-filter-item.selected .vendor-success-rate { color: #b3d9ff; }
        .tag-filter-item:hover { background: #e8e8e8; }
        .tag-filter-item.selected:hover { background: #0056b3; }
        .filter-actions { display: flex; justify-content: space-between; gap: 10px; margin-top: 20px; padding-top: 20px; border-top: 1px solid #eee; position: sticky; bottom: 0; background: white; z-index: 1001; }
        .filter-actions button { padding: 14px 20px; border: none; border-radius: 12px; cursor: pointer; font-size: 16px; flex: 1; background: #007AFF; color: white; font-weight: 600; transition: transform 0.2s; }
        .filter-actions button:active { transform: scale(0.98); }
        .rating { display: flex; align-items: center; gap: 4px; margin: 4px 0; }
        .rating-stars { color: #FF9500; }
        .rating-count { color: #666; font-size: 12px; }
        .game-tags { display: flex; flex-wrap: wrap; gap: 4px; margin: 6px 0; }
        .game-tag { background: #f5f5f5; color: #666; padding: 4px 8px; border-radius: 6px; font-size: 11px; }
        .view-mode-buttons { display: flex; gap: 8px; margin: 16px 0; }
        .view-mode-button { padding: 10px 16px; font-size: 14px; border: none; border-radius: 10px; background: #f0f0f0; color: #666; cursor: pointer; transition: all 0.2s; }
        .view-mode-button:active { transform: scale(0.98); }
        .view-mode-button.active { background: #007AFF; color: white; }
        .loading-spinner { display: none; text-align: center; padding: 20px; }
        .loading-spinner.active { display: block; }
        .loading-spinner::after { content: "加载中..."; color: #666; }
        .load-more-button { display: none; width: 100%; padding: 15px; margin: 20px 0; background: #f5f5f5; border: none; border-radius: 10px; color: #333; cursor: pointer; font-size: 16px; }
        .load-more-button:hover { background: #e8e8e8; }
        .load-more-button.show { display: block; }
        .screenshot-overlay { display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.8); z-index: 1000; }
        .screenshot-container { position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 90%; height: 90%; display: flex; justify-content: center; align-items: center; }
        .screenshot-grid { display: flex; gap: 20px; padding: 20px; max-width: 100%; overflow-x: auto; background: rgba(0,0,0,0.5); border-radius: 12px; -webkit-overflow-scrolling: touch; scrollbar-width: thin; scrollbar-color: rgba(255,255,255,0.3) transparent; }
        .screenshot-grid::-webkit-scrollbar { height: 8px; }
        .screenshot-grid::-webkit-scrollbar-track { background: transparent; }
        .screenshot-grid::-webkit-scrollbar-thumb { background-color: rgba(255,255,255,0.3); border-radius: 4px; }
        .screenshot-grid img { height: 80vh; width: auto; border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.2); }
        .screenshot-grid img:hover { transform: scale(1.02); transition: transform 0.2s; }
        .screenshot-close { position: fixed; top: 20px; right: 20px; width: 40px; height: 40px; border-radius: 50%; background: rgba(0,0,0,0.5); color: white; border: none; font-size: 24px; cursor: pointer; display: flex; align-items: center; justify-content: center; transition: all 0.2s; z-index: 1001; }
        .screenshot-close:hover { background: rgba(0,0,0,0.7); transform: scale(1.1); }
        .screenshot-overlay.active { display: block; }
        .toast-message { position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); background: rgba(0, 0, 0, 0.8); color: white; padding: 15px 30px; border-radius: 25px; font-size: 16px; opacity: 0; transition: all 0.3s ease; pointer-events: none; z-index: 2000; }
        .toast-message.show { opacity: 1; }
        .stats-info { font-size: 14px; color: #666; margin: 10px 0; padding: 10px; background: #f5f5f5; border-radius: 8px; }
        .stats-controls-row { display: flex; align-items: center; justify-content: space-between; }
        .stats-text { font-weight: 500; }
        
        /* 排序控件样式 */
        .sort-controls { display: flex; align-items: center; gap: 12px; }
        .sort-order-control { display: flex; align-items: center; }
        .sort-order-btn { display: flex; align-items: center; gap: 6px; padding: 8px 12px; background: #007AFF; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 14px; font-weight: 500; transition: all 0.2s; }
        .sort-order-btn:hover { background: #0056b3; transform: scale(1.02); }
        .sort-order-btn:active { transform: scale(0.98); }
        .sort-order-btn.asc .sort-order-icon { transform: rotate(180deg); }
        
        .sort-type-dropdown { position: relative; display: inline-block; }
        .sort-type-btn { display: flex; align-items: center; gap: 6px; padding: 8px 12px; background: #f5f5f5; border: 1px solid #ddd; border-radius: 6px; cursor: pointer; font-size: 14px; color: #333; font-weight: 500; transition: all 0.2s; }
        .sort-type-btn:hover { background: #e8e8e8; }
        .sort-type-btn.active { background: #007AFF; color: white; border-color: #007AFF; }
        .dropdown-arrow { font-size: 12px; transition: transform 0.2s; }
        .sort-type-dropdown.active .dropdown-arrow { transform: rotate(180deg); }
        
        .sort-type-content { display: none; position: absolute; top: 100%; left: 0; background: white; border: 1px solid #ddd; border-radius: 6px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); z-index: 1002; min-width: 120px; margin-top: 4px; }
        .sort-type-dropdown.active .sort-type-content { display: block; }
        .sort-type-option { padding: 8px 12px; cursor: pointer; font-size: 13px; transition: background 0.2s; }
        .sort-type-option:hover { background: #f5f5f5; }
        .sort-type-option.active { background: #007AFF; color: white; }
        .filter-conditions { margin: 10px 0; padding: 10px; background: #f8f8f8; border-radius: 8px; border: 1px solid #e0e0e0; }
        .filter-conditions-title { font-size: 14px; color: #333; margin-bottom: 0; margin-right: 8px; display: inline; }
        .filter-tags { display: inline-flex; flex-wrap: wrap; gap: 6px; }
        .filter-tag { display: inline-flex; align-items: center; gap: 4px; padding: 4px 8px; background: #007AFF; color: white; border-radius: 12px; font-size: 12px; cursor: pointer; transition: all 0.2s; }
        .filter-tag:hover { background: #0056b3; transform: scale(1.05); }
        .filter-tag .remove-btn { width: 16px; height: 16px; border-radius: 50%; background: rgba(255,255,255,0.3); border: none; color: white; font-size: 10px; cursor: pointer; display: flex; align-items: center; justify-content: center; transition: all 0.2s; }
        .filter-tag .remove-btn:hover { background: rgba(255,255,255,0.5); transform: scale(1.1); }
        .filter-tag.vendor-tag { background: #34C759; }
        .filter-tag.vendor-tag:hover { background: #28a745; }
        .filter-tag.time-tag { background: #FF9500; }
        .filter-tag.time-tag:hover { background: #e6850e; }
        .filter-tag.name-tag { background: #5856D6; }
        .filter-tag.name-tag:hover { background: #4a4a9e; }
        .filter-tag.genre-tag { background: #FF3B30; }
        .filter-tag.genre-tag:hover { background: #d63333; }
        .filter-tag.popularity-tag { background: #AF52DE; }
        .filter-tag.popularity-tag:hover { background: #8e44ad; }
        .filter-tag.no-filter { background: #999; }
        .filter-tag.no-filter:hover { background: #777; }
        .no-filters { font-size: 12px; color: #999; font-style: italic; }
        .vendor-header { padding: 15px; background: #f8f8f8; cursor: pointer; display: flex; justify-content: space-between; align-items: center; }
        .vendor-header:hover { background: #f0f0f0; }
        .vendor-header .vendor-title { display: flex; align-items: center; gap: 8px; }
        .vendor-header .vendor-name { font-size: 16px; font-weight: 600; color: #333; }
        .vendor-header .game-count { font-size: 14px; color: #666; }
        .vendor-header .toggle-icon { font-size: 20px; color: #666; transition: transform 0.3s; }
        .vendor-header.collapsed .toggle-icon { transform: rotate(-90deg); }
        .vendor-content { transition: max-height 0.3s ease-out; overflow: hidden; }
        .vendor-content.collapsed { max-height: 0 !important; }
        .game-list { min-height: 200px; }
        
        /* 人气筛选样式 */
        .popularity-filter-section label {
            border: 1px solid #ddd;
            transition: all 0.2s;
        }
        .popularity-filter-section label:hover {
            background: #e8e8e8 !important;
            border-color: #007AFF;
        }
        .popularity-filter-section input[type="radio"]:checked + span {
            color: #007AFF;
            font-weight: 600;
        }
        .popularity-filter-section input[type="radio"]:checked {
            accent-color: #007AFF;
        }
        .popularity-filter-section label:has(input[type="radio"]:checked) {
            background: #e6f3ff !important;
            border-color: #007AFF;
        }
        
        /* 游戏类型筛选模式样式 */
        .tag-filter-mode label {
            transition: all 0.2s;
        }
        .tag-filter-mode label:hover {
            transform: scale(1.05);
        }
        .tag-filter-mode input[type="radio"]:checked + span {
            font-weight: 600;
        }
        .tag-filter-mode input[type="radio"][value="include"]:checked + span {
            color: #007AFF;
        }
        .tag-filter-mode input[type="radio"][value="exclude"]:checked + span {
            color: #FF3B30;
        }
        .tag-filter-mode label:has(input[type="radio"][value="include"]:checked) {
            background: #e6f3ff !important;
            border-color: #007AFF;
        }
        .tag-filter-mode label:has(input[type="radio"][value="exclude"]:checked) {
            background: #ffe6e6 !important;
            border-color: #FF3B30;
        }
    </style>
</head>
<body>
    <!-- 加载进度显示 -->
    <div id="loading-progress" style="
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-align: center;
        font-size: 18px;
        color: #666;
        z-index: 1000;
    ">
        正在加载数据...
    </div>

    <!-- 主要内容容器，初始隐藏 -->
    <div class="container" style="display: none;">
        <h1>🎮 __TITLE__ (__DATE__)</h1>
    <div id="view-mode-bar" class="view-mode-buttons" style="margin: 20px 0;">
        <button class="view-mode-button active" data-mode="icon">📱 图标模式</button>
        <button class="view-mode-button" data-mode="vendor">🏢 厂商分类</button>
    </div>
    <div id="filter-conditions" class="filter-conditions">
        <div style="display: flex; align-items: center; flex-wrap: wrap;">
            <span class="filter-conditions-title">筛选条件：</span>
            <div id="filter-tags" class="filter-tags"></div>
        </div>
    </div>
    <div id="stats-info" class="stats-info">
        <div class="stats-controls-row">
            <div class="stats-text">显示 0 款游戏</div>
            <div class="sort-controls">
                <div class="sort-order-control">
                    <button id="sort-order-btn" class="sort-order-btn" title="切换排序方向">
                        <span id="sort-order-icon">↓</span>
                        <span id="sort-order-text">降序</span>
                    </button>
                </div>
                <div class="sort-type-dropdown">
                    <button id="sort-type-btn" class="sort-type-btn">
                        <span id="sort-type-text">厂商排名</span>
                        <span class="dropdown-arrow">▼</span>
                    </button>
                    <div id="sort-type-content" class="sort-type-content">
                        <div class="sort-type-option" data-sort="vendor_rank">厂商排名</div>
                        <div class="sort-type-option" data-sort="rating_count">评价数</div>
                        <div class="sort-type-option" data-sort="rating">评分</div>
                        <div class="sort-type-option" data-sort="release_date">上架时间</div>
                        <div class="sort-type-option" data-sort="update_date">更新时间</div>
                        <div class="sort-type-option" data-sort="import_time">入库时间</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div id="game-list"></div>
    <div id="loading-spinner" class="loading-spinner"></div>
    <button id="load-more" class="load-more-button">加载更多</button>
    <div id="filter-toggle" class="filter-toggle">🔍</div>
    <div id="filter-panel" class="filter-panel">
        <div class="filter-content">
            <div class="filter-header">
                <div class="filter-title">游戏筛选</div>
                <button class="filter-close">&times;</button>
            </div>
            <div class="filter-section name-filter-section">
                <div class="filter-section-title">游戏名称</div>
                <input type="text" id="name-filter" class="name-filter-input" placeholder="输入游戏名称或产品ID搜索...">
            </div>
            <div class="filter-section">
                <div class="vendor-section-header">
                    <div class="filter-section-title">时间筛选</div>
                    <div class="vendor-controls">
                        <button id="clear-time-range" style="padding: 6px 12px; background: #666; color: white; border: none; border-radius: 4px; font-size: 12px; cursor: pointer;">清除</button>
                    </div>
                </div>
                <div class="time-filter-section">
                    <div style="display: flex; gap: 10px; align-items: center; font-size: 14px;">
                        <select id="time-filter-type" style="padding: 6px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px; background: white; cursor: pointer;">
                            <option value="release">上架时间</option>
                            <option value="update">更新时间</option>
                            <option value="import_time" selected>入库时间</option>
                            <option value="delete_time">删除时间</option>
                        </select>
                        <input type="date" id="time-start-date" class="date-input" style="padding: 6px; border: 1px solid #ddd; border-radius: 4px; font-size: 12px;">
                        <span>至</span>
                        <input type="date" id="time-end-date" class="date-input" style="padding: 6px; border: 1px solid #ddd; border-radius: 4px; font-size: 12px;">
                    </div>
                </div>
            </div>
            <div class="filter-section">
                <div class="vendor-section-header">
                    <div class="filter-section-title">人气筛选</div>
                    <div class="vendor-controls">
                        <button id="clear-popularity-filter" style="padding: 6px 12px; background: #666; color: white; border: none; border-radius: 4px; font-size: 12px; cursor: pointer;">清除</button>
                    </div>
                </div>
                <div class="popularity-filter-section">
                    <div style="display: flex; flex-wrap: wrap; gap: 8px; font-size: 14px;">
                        <label style="display: flex; align-items: center; gap: 6px; cursor: pointer; padding: 6px 12px; border-radius: 6px; background: #f5f5f5; transition: all 0.2s;">
                            <input type="radio" name="popularity-filter" value="all" checked style="margin: 0;">
                            <span>全部</span>
                        </label>
                        <label style="display: flex; align-items: center; gap: 6px; cursor: pointer; padding: 6px 12px; border-radius: 6px; background: #f5f5f5; transition: all 0.2s;">
                            <input type="radio" name="popularity-filter" value="lt100" style="margin: 0;">
                            <span>小于1百</span>
                        </label>
                        <label style="display: flex; align-items: center; gap: 6px; cursor: pointer; padding: 6px 12px; border-radius: 6px; background: #f5f5f5; transition: all 0.2s;">
                            <input type="radio" name="popularity-filter" value="gt1000" style="margin: 0;">
                            <span>大于1千</span>
                        </label>
                        <label style="display: flex; align-items: center; gap: 6px; cursor: pointer; padding: 6px 12px; border-radius: 6px; background: #f5f5f5; transition: all 0.2s;">
                            <input type="radio" name="popularity-filter" value="gt10000" style="margin: 0;">
                            <span>大于1万</span>
                        </label>
                        <label style="display: flex; align-items: center; gap: 6px; cursor: pointer; padding: 6px 12px; border-radius: 6px; background: #f5f5f5; transition: all 0.2s;">
                            <input type="radio" name="popularity-filter" value="gt50000" style="margin: 0;">
                            <span>大于5万</span>
                        </label>
                        <label style="display: flex; align-items: center; gap: 6px; cursor: pointer; padding: 6px 12px; border-radius: 6px; background: #f5f5f5; transition: all 0.2s;">
                            <input type="radio" name="popularity-filter" value="gt100000" style="margin: 0;">
                            <span>大于10万</span>
                        </label>
                        <label style="display: flex; align-items: center; gap: 6px; cursor: pointer; padding: 6px 12px; border-radius: 6px; background: #f5f5f5; transition: all 0.2s;">
                            <input type="radio" name="popularity-filter" value="gt1000000" style="margin: 0;">
                            <span>大于1百万</span>
                        </label>
                    </div>
                </div>
            </div>
            <div class="filter-section">
                <div class="vendor-section-header">
                    <div class="filter-section-title">游戏类型</div>
                    <div class="vendor-controls">
                        <div class="tag-filter-mode" style="display: flex; gap: 8px; margin-right: 8px;">
                            <label style="display: flex; align-items: center; gap: 4px; cursor: pointer; font-size: 12px; padding: 4px 8px; border-radius: 4px; background: #e6f3ff; border: 1px solid #007AFF;">
                                <input type="radio" name="tag-filter-mode" value="include" checked style="margin: 0; accent-color: #007AFF;">
                                <span style="color: #007AFF; font-weight: 600;">包含</span>
                            </label>
                            <label style="display: flex; align-items: center; gap: 4px; cursor: pointer; font-size: 12px; padding: 4px 8px; border-radius: 4px; background: #f5f5f5; border: 1px solid #ddd;">
                                <input type="radio" name="tag-filter-mode" value="exclude" style="margin: 0; accent-color: #FF3B30;">
                                <span style="color: #666;">排除</span>
                            </label>
                        </div>
                        <button id="select-all-tags" class="select-top100-button" style="background: #007AFF;">全选</button>
                        <button id="invert-tags" class="select-top100-button" style="background: #FF9500;">反选</button>
                    </div>
                </div>
                <div class="tag-filter-grid" id="tag-filter-grid"></div>
            </div>
            <div class="filter-section">
                <div class="vendor-section-header">
                    <div class="filter-section-title">厂商</div>
                    <div class="vendor-controls">
                        <div class="sort-dropdown">
                            <button class="sort-dropdown-btn">综合排序</button>
                            <div class="sort-dropdown-content">
                                <div class="sort-option" data-sort="comprehensive">综合排序</div>
                                <div class="sort-option" data-sort="total_rating_count">总评分人数</div>
                                <div class="sort-option" data-sort="high_rating_products">成功产品数</div>
                            </div>
                        </div>
                        <div class="vendor-select-dropdown">
                            <button class="vendor-select-btn" onclick="toggleVendorSelectDropdown()">选择前N名 ▼</button>
                            <div class="vendor-select-content">
                                <div class="vendor-select-option" data-count="10" onclick="selectTopVendors(10)">选择前10</div>
                                <div class="vendor-select-option" data-count="50" onclick="selectTopVendors(50)">选择前50</div>
                                <div class="vendor-select-option" data-count="100" onclick="selectTopVendors(100)">选择前100</div>
                                <div class="vendor-select-option active" data-count="200" onclick="selectTopVendors(200)">选择前200</div>
                                <div class="vendor-select-option" data-count="300" onclick="selectTopVendors(300)">选择前300</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="vendor-search-section" style="margin-bottom: 12px;">
                    <input type="text" id="vendor-search" class="name-filter-input" placeholder="输入厂商名称搜索..." style="margin-bottom: 8px;">
                </div>
                <div class="tag-filter-grid" id="vendor-filter-grid"></div>
            </div>
            <div class="filter-actions">
                <button class="filter-apply" style="background: #34C759;">应用筛选</button>
            </div>
        </div>
    </div>
        <div id="screenshotOverlay" class="screenshot-overlay">
            <div class="screenshot-container"></div>
            <button class="screenshot-close">×</button>
        </div>
    </div> <!-- 关闭主要内容容器 -->

    <script>__JAVASCRIPT__</script>
</body>
</html>
