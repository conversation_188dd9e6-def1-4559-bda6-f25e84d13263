"""
HTML生成器模块
用于生成iOS游戏追踪器的HTML页面
"""

import os
import json
from datetime import datetime


# 安全的打印函数，处理编码问题
def safe_print(text):
    """安全的打印函数，处理Windows控制台编码问题"""
    try:
        print(text)
    except UnicodeEncodeError:
        # 如果遇到编码错误，移除特殊字符
        safe_text = text.encode('ascii', errors='ignore').decode('ascii')
        print(safe_text)


class HTMLGenerator:
    """HTML生成器类"""
    
    def __init__(self, template_dir=None):
        """
        初始化HTML生成器
        
        Args:
            template_dir: 模板文件目录，默认为当前文件所在目录
        """
        if template_dir is None:
            template_dir = os.path.dirname(__file__)
        self.template_dir = template_dir
        
        # 默认的App Store链接前缀
        self.dev_prefix = "https://apps.apple.com/developer/"
        self.app_prefix = "https://apps.apple.com/us/app/"
    
    def load_template(self, template_name):
        """
        加载模板文件
        
        Args:
            template_name: 模板文件名
            
        Returns:
            str: 模板内容
            
        Raises:
            FileNotFoundError: 模板文件不存在
        """
        template_path = os.path.join(self.template_dir, template_name)
        
        try:
            with open(template_path, "r", encoding="utf-8") as f:
                return f.read()
        except FileNotFoundError:
            raise FileNotFoundError(f"模板文件不存在: {template_path}")
    
    def split_data_into_chunks(self, history, chunk_size=100):
        """
        将历史数据分割成多个块

        Args:
            history: 应用历史数据
            chunk_size: 每个块包含的开发者数量

        Returns:
            list: 数据块列表
        """
        print(f"   开始分割数据，每块 {chunk_size} 个开发者...")

        chunks = []
        current_chunk = {}
        current_count = 0

        for dev_id, apps_data in history.items():
            current_chunk[dev_id] = apps_data
            current_count += 1

            if current_count >= chunk_size:
                chunks.append(current_chunk)
                current_chunk = {}
                current_count = 0

        # 添加最后一个块（如果有剩余数据）
        if current_chunk:
            chunks.append(current_chunk)

        print(f"   数据分割完成，共 {len(chunks)} 个块")
        return chunks

    def save_json_chunks(self, chunks, output_dir, main_vendors_with_scores, vendor_stats):
        """
        保存JSON数据块到文件

        Args:
            chunks: 数据块列表
            output_dir: 输出目录
            main_vendors_with_scores: 主要厂商列表（带分数）
            vendor_stats: 厂商统计信息

        Returns:
            dict: 包含文件信息的字典
        """
        print(f"   开始保存JSON文件到目录: {output_dir}")

        # 创建data子目录
        data_dir = os.path.join(output_dir, "data")
        os.makedirs(data_dir, exist_ok=True)

        # 保存数据块
        chunk_files = []
        for i, chunk in enumerate(chunks):
            chunk_file = f"apps_chunk_{i:03d}.json"
            chunk_path = os.path.join(data_dir, chunk_file)

            try:
                with open(chunk_path, 'w', encoding='utf-8') as f:
                    json.dump(chunk, f, ensure_ascii=False, separators=(',', ':'))
                chunk_files.append(chunk_file)
                print(f"   保存数据块 {i+1}/{len(chunks)}: {chunk_file}")
            except Exception as e:
                print(f"   保存数据块 {i+1} 失败: {e}")
                raise

        # 保存主要厂商数据
        vendors_file = "main_vendors.json"
        vendors_path = os.path.join(data_dir, vendors_file)
        try:
            with open(vendors_path, 'w', encoding='utf-8') as f:
                json.dump(main_vendors_with_scores, f, ensure_ascii=False, separators=(',', ':'))
            print(f"   保存主要厂商数据: {vendors_file}")
        except Exception as e:
            print(f"   保存主要厂商数据失败: {e}")
            raise

        # 保存厂商统计数据
        stats_file = "vendor_stats.json"
        stats_path = os.path.join(data_dir, stats_file)
        try:
            with open(stats_path, 'w', encoding='utf-8') as f:
                json.dump(vendor_stats, f, ensure_ascii=False, separators=(',', ':'))
            print(f"   保存厂商统计数据: {stats_file}")
        except Exception as e:
            print(f"   保存厂商统计数据失败: {e}")
            raise

        # 保存数据清单
        manifest = {
            "chunk_files": chunk_files,
            "vendors_file": vendors_file,
            "stats_file": stats_file,
            "total_chunks": len(chunks)
        }

        manifest_path = os.path.join(data_dir, "manifest.json")
        try:
            with open(manifest_path, 'w', encoding='utf-8') as f:
                json.dump(manifest, f, ensure_ascii=False, indent=2)
            print(f"   保存数据清单: manifest.json")
        except Exception as e:
            print(f"   保存数据清单失败: {e}")
            raise

        return manifest
    
    def generate_javascript(self, js_template, manifest):
        """
        生成JavaScript代码（使用外部JSON文件）

        Args:
            js_template: JavaScript模板
            manifest: 数据清单信息

        Returns:
            str: 生成的JavaScript代码
        """
        print(f"   开始替换JavaScript模板变量...")
        try:
            # 生成数据清单的JSON字符串
            manifest_json = json.dumps(manifest, ensure_ascii=False, separators=(',', ':'))

            js_code = js_template.replace('__DATA_MANIFEST__', manifest_json) \
                                .replace('__APP_PREFIX__', f'"{self.app_prefix}"') \
                                .replace('__DEV_PREFIX__', f'"{self.dev_prefix}"')
            print(f"   JavaScript模板变量替换完成")
            return js_code
        except Exception as e:
            print(f"   JavaScript模板变量替换失败: {e}")
            raise
    
    def generate_html_content(self, html_template, title="iOS游戏库"):
        """
        生成HTML内容（不包含JavaScript）
        
        Args:
            html_template: HTML模板
            title: 页面标题
            
        Returns:
            str: HTML内容
        """
        print(f"   开始替换HTML模板变量...")
        try:
            print(f"   替换 __TITLE__...")
            html_content = html_template.replace('__TITLE__', title)
            print(f"   替换 __DATE__...")
            html_content = html_content.replace('__DATE__', datetime.now().strftime("%Y/%m/%d"))
            print(f"   HTML模板变量替换完成")
            return html_content
        except Exception as e:
            print(f"   HTML模板变量替换失败: {e}")
            import traceback
            print(f"   详细错误信息: {traceback.format_exc()}")
            raise
    
    def write_html_file(self, output_file, html_content, js_code):
        """
        写入HTML文件
        
        Args:
            output_file: 输出文件路径
            html_content: HTML内容
            js_code: JavaScript代码
        """
        print(f"   开始分段写入HTML文件...")
        try:
            # 使用 split 方法将模板内容分割成两部分，这比 replace 内存效率高得多
            parts = html_content.split('__JAVASCRIPT__', 1)

            with open(output_file, "w", encoding="utf-8") as f:
                # 写入占位符之前的部分
                f.write(parts[0])
                
                # 直接写入巨大的 js_code 字符串
                print(f"   正在写入 __JAVASCRIPT__ 内容...")
                f.write(js_code)
                
                # 如果占位符后面还有内容，则写入剩余部分
                if len(parts) > 1:
                    f.write(parts[1])
                    
            print(f"   HTML文件写入完成")
        except Exception as e:
            print(f"   HTML文件写入失败: {e}")
            import traceback
            print(f"   详细错误信息: {traceback.format_exc()}")
            raise
    
    def generate_html(self, history, output_file, main_vendors_with_scores, vendor_stats, title="iOS游戏库", chunk_size=100):
        """
        生成HTML文件（使用外部JSON文件）

        Args:
            history: 应用历史数据
            output_file: 输出文件路径
            main_vendors_with_scores: 主要厂商列表（带分数）
            vendor_stats: 厂商统计信息
            title: 页面标题
            chunk_size: 每个JSON文件包含的开发者数量
        """
        print(f"   开始生成HTML，输出文件: {output_file}")

        # 确保输出路径不为空
        if not output_file:
            raise ValueError("输出文件路径不能为空")

        # 获取输出文件的目录路径
        output_dir = os.path.dirname(output_file)
        # 如果目录路径为空，使用当前目录
        if not output_dir:
            output_dir = "."

        print(f"   输出目录: {output_dir}")

        # 创建输出目录
        try:
            os.makedirs(output_dir, exist_ok=True)
            print(f"   输出目录创建/确认完成")
        except Exception as e:
            print(f"   创建输出目录失败: {e}")
            raise

        # 分割数据并保存为JSON文件
        chunks = self.split_data_into_chunks(history, chunk_size)
        manifest = self.save_json_chunks(chunks, output_dir, main_vendors_with_scores, vendor_stats)

        # 加载模板文件
        html_template = self.load_template("ios_game_tracker_html.tmpl")
        js_template = self.load_template("ios_game_tracker_js.tmpl")

        # 生成JavaScript代码
        js_code = self.generate_javascript(js_template, manifest)

        # 生成HTML内容
        html_content = self.generate_html_content(html_template, title)

        # 写入HTML文件
        self.write_html_file(output_file, html_content, js_code)

        print(f"   HTML生成完成: {output_file}")
        print(f"   数据文件已保存到: {os.path.join(output_dir, 'data')}")
        print(f"   共生成 {len(chunks)} 个数据文件")


def calculate_vendor_stats(history):
    """计算每个厂商的统计信息：总产品数、500+评分产品数、成功率"""

    vendor_stats = {}

    # 遍历所有开发者数据
    for dev_data in history.values():
        for app_data in dev_data.values():
            if isinstance(app_data, dict):

                # 处理iTunes API原始数据格式
                if 'trackId' in app_data:
                    # 这是iTunes API原始数据
                    vendor_name = app_data.get('artistName', '')
                    rating_count = app_data.get('userRatingCount', '0')
                else:
                    # 这是已处理的数据格式
                    vendor_name = app_data.get('developer', {}).get('name', '')
                    rating_count = app_data.get('Rating Count', '0')

                # 转换评分人数为整数
                try:
                    rating_count_int = int(rating_count) if rating_count else 0
                except (ValueError, TypeError):
                    rating_count_int = 0

                if vendor_name:
                    if vendor_name not in vendor_stats:
                        vendor_stats[vendor_name] = {
                            'total_products': 0,
                            'high_rating_products': 0,
                            'total_rating_count': 0,
                            'success_rate': 0.0
                        }

                    # 总产品数+1
                    vendor_stats[vendor_name]['total_products'] += 1

                    # 累加总评分人数
                    vendor_stats[vendor_name]['total_rating_count'] += rating_count_int

                    # 如果评分人数大于等于500，则500+产品数+1
                    if rating_count_int >= 500:
                        vendor_stats[vendor_name]['high_rating_products'] += 1

    # 计算成功率
    for vendor_name, stats in vendor_stats.items():
        if stats['total_products'] > 0:
            stats['success_rate'] = (stats['high_rating_products'] / stats['total_products']) * 100
        else:
            stats['success_rate'] = 0.0

    return vendor_stats


def get_main_vendors(history, top_n=50):
    """根据评分人数500以上的产品数量获取主要厂商列表（前N名）"""
    vendor_stats = calculate_vendor_stats(history)

    # 按评分人数500以上的产品数量排序，取前N名
    sorted_vendors = sorted(vendor_stats.items(), key=lambda x: x[1]['high_rating_products'], reverse=True)
    top_vendors = sorted_vendors[:top_n]

    # 返回格式化的厂商列表：["Voodoo(27)", "HABBY(21)", ...]（数字表示500+评分的产品数量）
    main_vendors_with_scores = [f"{vendor}({stats['high_rating_products']})" for vendor, stats in top_vendors]

    # 同时返回不带分数的厂商名称列表，用于匹配
    main_vendor_names = [vendor for vendor, stats in top_vendors]

    # 返回完整的统计信息
    return main_vendors_with_scores, main_vendor_names, vendor_stats


def load_all_json_files(directory, main_vendor_names=None):
    """加载目录下的所有 JSON 文件，保持原始数据完整性"""
    import glob

    all_data = {}
    # 只读取目录下的json文件，不递归读取子目录
    json_files = glob.glob(os.path.join(directory, "*.json"))
    for json_file in json_files:
        try:
            with open(json_file, "r", encoding="utf-8") as f:
                data = json.load(f)
                filename = os.path.basename(json_file)
                # 使用文件名（不含扩展名）作为key
                key = os.path.splitext(filename)[0]

                # 处理新的JSON格式（iTunes API原始数据）
                if "results" in data:
                    apps_data = {}
                    for item in data["results"]:
                        if "trackId" in item:  # 这是一个应用
                            app_id = str(item["trackId"])
                            # 保存完整的原始数据
                            apps_data[app_id] = item.copy()
                    all_data[key] = apps_data
                else:
                    # 处理已合并的数据格式（包含入库时间和删除时间）
                    # 不再跳过删除的应用，让它们也能显示
                    all_data[key] = data
        except Exception as e:
            print(f"Error loading {json_file}: {e}")
    return all_data


def convert_full_data_to_display_format(full_data, main_vendor_names=None):
    """将完整的原始数据转换为HTML显示所需的格式"""
    print(f"   convert_full_data_to_display_format: 开始转换数据，共 {len(full_data)} 个开发者")
    display_data = {}

    processed_count = 0
    for dev_id, apps_data in full_data.items():
        display_apps = {}

        for app_id, app_info in apps_data.items():
            # 不再跳过已删除的应用，让它们也能显示
            # if 'delete_time' in app_info:
            #     continue

            # 检查是否为iTunes API原始数据格式
            if isinstance(app_info, dict) and 'trackId' in app_info:
                # 这是iTunes API原始数据
                rating = float(app_info.get("averageUserRating", "0"))
                rating_count = app_info.get("userRatingCount", "0")
                rating_stars = "★" * int(rating) + "☆" * (5 - int(rating))

                # 获取截图URL
                screenshots = []
                if "screenshotUrls" in app_info:
                    screenshots = app_info["screenshotUrls"]
                elif "screenshots" in app_info:
                    screenshots = app_info["screenshots"]

                # 检查是否有"Games"标签，如果没有则跳过这个应用
                genres = app_info.get("genres", [])
                if "Games" not in genres:
                    continue  # 跳过没有Games标签的应用

                # 过滤掉"Games"标签（用于显示）
                filtered_genres = [genre for genre in genres if genre != "Games"]

                # 获取开发者信息
                developer_name = app_info.get("artistName", "")
                # 检查开发者名称是否在主要厂商列表中（模糊匹配）
                matched_vendor = None
                if main_vendor_names:
                    for vendor in main_vendor_names:
                        if vendor.lower() in developer_name.lower() or developer_name.lower() in vendor.lower():
                            matched_vendor = vendor
                            break

                developer_info = {
                    "name": matched_vendor if matched_vendor else developer_name,
                    "url": app_info.get("artistViewUrl", "")
                }

                display_apps[app_id] = {
                    "name": app_info.get("trackName", "未知"),
                    "Released on": app_info.get("releaseDate", ""),
                    "Updated on": app_info.get("currentVersionReleaseDate", ""),
                    "Downloads": "未知",
                    "Rating": f"{rating:.2f}",
                    "Rating Count": str(rating_count),
                    "Icon URL": app_info.get("artworkUrl512", ""),
                    "Stat": f"{rating:.2f} {rating_stars} ({rating_count})",
                    "screenshotUrls": screenshots[:5],  # 只取前5张截图
                    "developer": developer_info,
                    "genres": filtered_genres,
                    "trackViewUrl": app_info.get("trackViewUrl", ""),
                    "import_time": app_info.get("import_time", ""),
                    "delete_time": app_info.get("delete_time", "")
                }
            else:
                # 这是已经处理过的数据格式，直接使用
                display_apps[app_id] = app_info

        display_data[dev_id] = display_apps
        processed_count += 1
        if processed_count % 100 == 0:
            print(f"   convert_full_data_to_display_format: 已处理 {processed_count}/{len(full_data)} 个开发者")

    print(f"   convert_full_data_to_display_format: 数据转换完成，共处理 {processed_count} 个开发者")
    return display_data


def generate_html_for_directory(data_directory, output_file, title="iOS游戏库", chunk_size=100):
    """
    为指定目录生成HTML文件的便捷函数

    Args:
        data_directory: 数据目录路径
        output_file: 输出HTML文件路径
        title: 页面标题
        chunk_size: 每个JSON文件包含的开发者数量
    """
    safe_print(f"为目录生成HTML: {data_directory}")

    # 检查数据目录是否存在
    if not os.path.exists(data_directory):
        raise FileNotFoundError(f"数据目录不存在: {data_directory}")

    # 首先加载完整数据以计算主要厂商
    print("   正在计算主要厂商...")
    full_history = load_all_json_files(data_directory)

    if not full_history:
        print("   警告：数据目录中没有找到JSON文件")
        # 创建空的数据结构
        main_vendors_with_scores = []
        main_vendor_names = []
        vendor_stats = {}
        display_history = {}
    else:
        main_vendors_with_scores, main_vendor_names, vendor_stats = get_main_vendors(full_history, 50)
        print(f"   已识别前50主要厂商，500+评分产品数量最多的是: {main_vendors_with_scores[0] if main_vendors_with_scores else '无'}")

        # 将完整数据转换为HTML显示格式
        print("   正在转换数据格式...")
        try:
            display_history = convert_full_data_to_display_format(full_history, main_vendor_names)
            print(f"   数据格式转换完成，共 {len(display_history)} 个开发者")
        except Exception as e:
            print(f"   数据格式转换失败: {e}")
            raise

    # 创建HTML生成器并生成文件
    try:
        print("   正在生成HTML文件...")
        generator = HTMLGenerator()
        generator.generate_html(display_history, output_file, main_vendors_with_scores, vendor_stats, title, chunk_size)
        safe_print(f"HTML 已生成: {output_file}")
    except Exception as e:
        print(f"   HTML生成失败: {e}")
        raise

    # 统计信息
    total_apps = sum(len(apps) for apps in display_history.values())
    total_developers = len(display_history)
    safe_print(f"统计信息:")
    print(f"   - 开发者数量: {total_developers}")
    print(f"   - 应用总数: {total_apps}")
    if total_developers > 0:
        print(f"   - 平均每个开发者: {total_apps/total_developers:.1f} 款应用")
    print(f"   - 主要厂商数量: {len(main_vendors_with_scores)}")
    if main_vendors_with_scores:
        print(f"   - 500+评分产品最多的厂商: {main_vendors_with_scores[0]}")
        print(f"   - 500+评分产品最少的主要厂商: {main_vendors_with_scores[-1]}")

    return output_file
