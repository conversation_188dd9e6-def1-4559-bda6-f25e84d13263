<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>iOS游戏库</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", sans-serif; margin: 10px; font-size: 15px; }
        details { margin-bottom: 20px; }
        details summary { padding: 10px; background: #f5f5f5; border-radius: 8px; cursor: pointer; margin-bottom: 10px; }
        details summary:hover { background: #e8e8e8; }
        .app-grid { display: flex; flex-wrap: wrap; gap: 16px; padding: 16px; min-height: 100px; }
        .app-grid.list-mode { display: block; }
        .app-grid.icon-mode { display: flex; flex-wrap: wrap; gap: 16px; padding: 16px; }
        .app-item { flex: 0 0 160px; padding: 10px; border: 1px solid #ddd; border-radius: 8px; font-size: 14px; background: #fff; box-shadow: 0 1px 3px rgba(0,0,0,0.1); }
        .app-item.list-mode { display: grid; grid-template-columns: 80px 200px 1fr 1fr 1fr; gap: 20px; align-items: center; margin-bottom: 10px; flex: none; width: auto; }
        .app-item.icon-mode { flex: 0 0 160px; padding: 10px; display: flex; flex-direction: column; gap: 4px; }
        .app-item.icon-mode:hover { transform: translateY(-2px); box-shadow: 0 4px 8px rgba(0,0,0,0.1); }
        .app-item.icon-mode .app-icon { width: 80px; height: 80px; margin: 0 auto 8px; }
        .app-item.icon-mode a.app-name { font-size: 14px; font-weight: bold; margin-bottom: 2px; overflow: hidden; text-overflow: ellipsis; display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical; min-height: 36px; color: #000000; text-decoration: none; }
        .app-item.icon-mode .app-id { font-size: 11px; color: #999; margin-bottom: 2px; }
        .app-item.icon-mode a.developer-name { font-size: 12px; color: #666; text-decoration: none; overflow: hidden; text-overflow: ellipsis; display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical; min-height: 24px; margin-bottom: 4px; }
        .app-item.icon-mode a.developer-name:hover { color: #007AFF; text-decoration: underline; }
        .app-item.icon-mode .app-date { font-size: 12px; color: #666; }
        .app-item.icon-mode .rating { display: flex; align-items: center; gap: 4px; }
        .app-item.icon-mode .game-tags { display: flex; flex-wrap: wrap; gap: 4px; margin-top: 2px; }
        .app-item.icon-mode .game-tag { font-size: 11px; padding: 2px 6px; background: #f5f5f5; border-radius: 4px; color: #666; }
        .app-item.list-mode .app-icon { width: 80px; height: 80px; margin: 0; }
        .app-item.list-mode .app-info { display: flex; flex-direction: column; gap: 4px; }
        .app-item.list-mode .app-id { font-size: 11px; color: #999; margin-bottom: 2px; }
        .app-item.list-mode .app-dates { display: flex; flex-direction: column; gap: 4px; }
        .app-item.list-mode .app-stats { display: flex; flex-direction: column; gap: 4px; }
        .app-item.list-mode .game-tags { margin: 0; }
        .app-item a { font-weight: bold; display: block; margin-bottom: 5px; color: #000000; text-decoration: none; }
        .app-item a:hover { color: #007AFF; }
        .app-date { font-size: 12px; color: #666; }
        @media (max-width: 1200px) {
          .app-item { flex: 0 0 140px; }
          .app-item.icon-mode { flex: 0 0 120px; }
        }
        @media (max-width: 768px) {
          body { padding: 12px; }
          .app-grid { gap: 10px; padding: 12px; }
          .app-item { padding: 10px; }
          .app-item.icon-mode { flex: 0 0 calc(33.33% - 8px); }
          .filter-content { padding: 16px; }
          .tag-filter-grid { grid-template-columns: repeat(auto-fill, minmax(120px, 1fr)); }
          .tag-filter-item { padding: 6px 8px; font-size: 12px; }
        }
        @media (max-width: 480px) {
          .app-grid { gap: 8px; padding: 8px; }
          .app-item { padding: 8px; }
          .app-item.icon-mode { flex: 0 0 calc(42%); }
          .filter-toggle { width: 48px; height: 48px; border-radius: 24px; font-size: 20px; }
        }
        @media (max-width: 360px) {
          .app-item.icon-mode { flex: 0 0 100%; }
        }
        .vendor-header { font-size: 16px; font-weight: bold; margin-top: 20px; }
        .filter-buttons { display: flex; flex-wrap: wrap; gap: 6px; margin-bottom: 10px; }
        .filter-button { padding: 6px 12px; font-size: 14px; border: none; border-radius: 6px; background: #007bff; color: white; cursor: pointer; }
        .filter-button.active { background: #0056b3; }
        h1 { font-size: 24px; margin: 0 0 20px 0; color: #333; }
        .vendor-section { margin-bottom: 16px; background: white; border-radius: 12px; overflow: hidden; box-shadow: 0 1px 3px rgba(0,0,0,0.1); }
        .vendor-header a { color: #333; text-decoration: none; }
        .vendor-header a:hover { color: #007AFF; }
        .vendor-header .game-count { font-size: 14px; color: #666; font-weight: normal; }
        .app-icon { width: 100%; height: auto; aspect-ratio: 1; border-radius: 22%; margin-bottom: 8px; cursor: pointer; max-width: 120px; margin: 0 auto 8px; display: block; }
        .app-icon.has-screenshots { border: 3px solid #34C759; box-shadow: 0 2px 8px rgba(52, 199, 89, 0.2); }
        .filter-toggle { position: fixed; bottom: 20px; right: 20px; width: 56px; height: 56px; border-radius: 28px; background: #007AFF; color: white; border: none; font-size: 24px; box-shadow: 0 2px 8px rgba(0,0,0,0.2); z-index: 100; cursor: pointer; display: flex; align-items: center; justify-content: center; transition: transform 0.2s; }
        .filter-toggle:active { transform: scale(0.95); }
        .filter-panel { display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; overflow: hidden; }
        .filter-panel.active { display: block; }
        .filter-content { position: fixed; bottom: 0; left: 0; width: 100%; max-width: 100%; background: white; border-radius: 20px 20px 0 0; padding: 20px; box-shadow: 0 -2px 10px rgba(0,0,0,0.1); max-height: 85vh; overflow-y: auto; -webkit-overflow-scrolling: touch; box-sizing: border-box; }
        .filter-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; padding-bottom: 15px; border-bottom: 1px solid #eee; position: sticky; top: 0; background: white; z-index: 1001; }
        .filter-title { font-size: 20px; font-weight: 600; color: #333; background: linear-gradient(45deg, #007AFF, #5856D6); -webkit-background-clip: text; -webkit-text-fill-color: transparent; }
        .vendor-section-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px; }
        .vendor-controls { display: flex; align-items: center; gap: 8px; }
        .sort-dropdown { position: relative; display: inline-block; }
        .sort-dropdown-btn { background: #f5f5f5; border: 1px solid #ddd; border-radius: 6px; padding: 6px 12px; cursor: pointer; font-size: 12px; transition: all 0.2s; color: #333; font-weight: 500; }
        .sort-dropdown-btn:hover { background: #e8e8e8; }
        .sort-dropdown-content { display: none; position: absolute; top: 100%; left: 0; background: white; border: 1px solid #ddd; border-radius: 6px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); z-index: 1002; min-width: 120px; }
        .sort-dropdown.active .sort-dropdown-content { display: block; }
        .sort-option { padding: 8px 12px; cursor: pointer; font-size: 13px; transition: background 0.2s; }
        .sort-option:hover { background: #f5f5f5; }
        .sort-option.active { background: #007AFF; color: white; }
        .select-top100-button { padding: 6px 12px; font-size: 12px; border: none; border-radius: 6px; background: #34C759; color: white; cursor: pointer; font-weight: 600; transition: all 0.2s; }
        .select-top100-button:hover { background: #28a745; transform: scale(1.02); }
        .select-top100-button:active { transform: scale(0.98); }
        #select-all-tags:hover { background: #0056b3 !important; }
        #invert-tags:hover { background: #e6850e !important; }
        .vendor-select-dropdown { position: relative; display: inline-block; }
        .vendor-select-btn { background: #34C759; border: none; border-radius: 6px; padding: 6px 12px; cursor: pointer; font-size: 12px; color: white; font-weight: 600; transition: all 0.2s; }
        .vendor-select-btn:hover { background: #28a745; transform: scale(1.02); }
        .vendor-select-btn:active { transform: scale(0.98); }
        .vendor-select-content { display: none; position: absolute; top: 100%; left: 0; background: white; border: 1px solid #ddd; border-radius: 6px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); z-index: 1002; min-width: 120px; }
        .vendor-select-dropdown.active .vendor-select-content { display: block; }
        .vendor-select-option { padding: 8px 12px; cursor: pointer; font-size: 13px; transition: background 0.2s; }
        .vendor-select-option:hover { background: #f5f5f5; }
        .vendor-select-option.active { background: #34C759; color: white; }
        .filter-close { background: none; border: none; font-size: 24px; color: #666; cursor: pointer; padding: 8px; margin: -8px; }
        .filter-section { margin-bottom: 24px; }
        .filter-section-title { font-size: 16px; font-weight: 600; color: #333; margin-bottom: 12px; display: flex; align-items: center; }
        .filter-section-title::before { content: ""; display: inline-block; width: 4px; height: 16px; background: #007AFF; margin-right: 8px; border-radius: 2px; }
        .name-filter-section { margin-bottom: 20px; box-sizing: border-box; width: 100%; }
        .name-filter-input { width: 100%; padding: 12px 16px; border: 2px solid #e8e8e8; border-radius: 12px; font-size: 15px; transition: all 0.3s; background: #f8f8f8; box-sizing: border-box; }
        .name-filter-input:focus { outline: none; border-color: #007AFF; background: white; box-shadow: 0 2px 8px rgba(0,123,255,0.1); }
        .name-filter-input::placeholder { color: #999; }
        .tag-filter-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(140px, 1fr)); gap: 8px; padding: 4px; }
        .tag-filter-item { display: flex; align-items: center; justify-content: center; padding: 8px 12px; border-radius: 8px; background: #f5f5f5; color: #666; font-size: 14px; cursor: pointer; user-select: none; transition: all 0.2s; border: none; text-align: center; min-height: 36px; position: relative; }
        .tag-filter-item input[type="checkbox"] { cursor: pointer; margin: 0; }
        .vendor-display { display: flex; flex-direction: column; align-items: center; gap: 2px; width: 100%; }
        .vendor-header-row { display: flex; align-items: center; justify-content: space-between; width: 100%; margin-bottom: 2px; }
        .vendor-rank { font-size: 10px; color: #999; font-weight: 600; }
        .vendor-name { font-weight: 600; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; max-width: 110px; }
        .vendor-total-rating { font-size: 11px; color: #888; font-weight: 400; }
        .vendor-success-rate { font-size: 11px; color: #888; }

        .tag-filter-item:active { transform: scale(0.98); }
        .tag-filter-item.selected { background: #007AFF; color: white; }
        .tag-filter-item.selected .vendor-rank { color: #e6f3ff; }
        .tag-filter-item.selected .vendor-total-rating { color: #cce7ff; }
        .tag-filter-item.selected .vendor-success-rate { color: #b3d9ff; }
        .tag-filter-item:hover { background: #e8e8e8; }
        .tag-filter-item.selected:hover { background: #0056b3; }
        .filter-actions { display: flex; justify-content: space-between; gap: 10px; margin-top: 20px; padding-top: 20px; border-top: 1px solid #eee; position: sticky; bottom: 0; background: white; z-index: 1001; }
        .filter-actions button { padding: 14px 20px; border: none; border-radius: 12px; cursor: pointer; font-size: 16px; flex: 1; background: #007AFF; color: white; font-weight: 600; transition: transform 0.2s; }
        .filter-actions button:active { transform: scale(0.98); }
        .rating { display: flex; align-items: center; gap: 4px; margin: 4px 0; }
        .rating-stars { color: #FF9500; }
        .rating-count { color: #666; font-size: 12px; }
        .game-tags { display: flex; flex-wrap: wrap; gap: 4px; margin: 6px 0; }
        .game-tag { background: #f5f5f5; color: #666; padding: 4px 8px; border-radius: 6px; font-size: 11px; }
        .view-mode-buttons { display: flex; gap: 8px; margin: 16px 0; }
        .view-mode-button { padding: 10px 16px; font-size: 14px; border: none; border-radius: 10px; background: #f0f0f0; color: #666; cursor: pointer; transition: all 0.2s; }
        .view-mode-button:active { transform: scale(0.98); }
        .view-mode-button.active { background: #007AFF; color: white; }
        .loading-spinner { display: none; text-align: center; padding: 20px; }
        .loading-spinner.active { display: block; }
        .loading-spinner::after { content: "加载中..."; color: #666; }
        .load-more-button { display: none; width: 100%; padding: 15px; margin: 20px 0; background: #f5f5f5; border: none; border-radius: 10px; color: #333; cursor: pointer; font-size: 16px; }
        .load-more-button:hover { background: #e8e8e8; }
        .load-more-button.show { display: block; }
        .screenshot-overlay { display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.8); z-index: 1000; }
        .screenshot-container { position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 90%; height: 90%; display: flex; justify-content: center; align-items: center; }
        .screenshot-grid { display: flex; gap: 20px; padding: 20px; max-width: 100%; overflow-x: auto; background: rgba(0,0,0,0.5); border-radius: 12px; -webkit-overflow-scrolling: touch; scrollbar-width: thin; scrollbar-color: rgba(255,255,255,0.3) transparent; }
        .screenshot-grid::-webkit-scrollbar { height: 8px; }
        .screenshot-grid::-webkit-scrollbar-track { background: transparent; }
        .screenshot-grid::-webkit-scrollbar-thumb { background-color: rgba(255,255,255,0.3); border-radius: 4px; }
        .screenshot-grid img { height: 80vh; width: auto; border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.2); }
        .screenshot-grid img:hover { transform: scale(1.02); transition: transform 0.2s; }
        .screenshot-close { position: fixed; top: 20px; right: 20px; width: 40px; height: 40px; border-radius: 50%; background: rgba(0,0,0,0.5); color: white; border: none; font-size: 24px; cursor: pointer; display: flex; align-items: center; justify-content: center; transition: all 0.2s; z-index: 1001; }
        .screenshot-close:hover { background: rgba(0,0,0,0.7); transform: scale(1.1); }
        .screenshot-overlay.active { display: block; }
        .toast-message { position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); background: rgba(0, 0, 0, 0.8); color: white; padding: 15px 30px; border-radius: 25px; font-size: 16px; opacity: 0; transition: all 0.3s ease; pointer-events: none; z-index: 2000; }
        .toast-message.show { opacity: 1; }
        .stats-info { font-size: 14px; color: #666; margin: 10px 0; padding: 10px; background: #f5f5f5; border-radius: 8px; }
        .stats-controls-row { display: flex; align-items: center; justify-content: space-between; }
        .stats-text { font-weight: 500; }
        
        /* 排序控件样式 */
        .sort-controls { display: flex; align-items: center; gap: 12px; }
        .sort-order-control { display: flex; align-items: center; }
        .sort-order-btn { display: flex; align-items: center; gap: 6px; padding: 8px 12px; background: #007AFF; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 14px; font-weight: 500; transition: all 0.2s; }
        .sort-order-btn:hover { background: #0056b3; transform: scale(1.02); }
        .sort-order-btn:active { transform: scale(0.98); }
        .sort-order-btn.asc .sort-order-icon { transform: rotate(180deg); }
        
        .sort-type-dropdown { position: relative; display: inline-block; }
        .sort-type-btn { display: flex; align-items: center; gap: 6px; padding: 8px 12px; background: #f5f5f5; border: 1px solid #ddd; border-radius: 6px; cursor: pointer; font-size: 14px; color: #333; font-weight: 500; transition: all 0.2s; }
        .sort-type-btn:hover { background: #e8e8e8; }
        .sort-type-btn.active { background: #007AFF; color: white; border-color: #007AFF; }
        .dropdown-arrow { font-size: 12px; transition: transform 0.2s; }
        .sort-type-dropdown.active .dropdown-arrow { transform: rotate(180deg); }
        
        .sort-type-content { display: none; position: absolute; top: 100%; left: 0; background: white; border: 1px solid #ddd; border-radius: 6px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); z-index: 1002; min-width: 120px; margin-top: 4px; }
        .sort-type-dropdown.active .sort-type-content { display: block; }
        .sort-type-option { padding: 8px 12px; cursor: pointer; font-size: 13px; transition: background 0.2s; }
        .sort-type-option:hover { background: #f5f5f5; }
        .sort-type-option.active { background: #007AFF; color: white; }
        .filter-conditions { margin: 10px 0; padding: 10px; background: #f8f8f8; border-radius: 8px; border: 1px solid #e0e0e0; }
        .filter-conditions-title { font-size: 14px; color: #333; margin-bottom: 0; margin-right: 8px; display: inline; }
        .filter-tags { display: inline-flex; flex-wrap: wrap; gap: 6px; }
        .filter-tag { display: inline-flex; align-items: center; gap: 4px; padding: 4px 8px; background: #007AFF; color: white; border-radius: 12px; font-size: 12px; cursor: pointer; transition: all 0.2s; }
        .filter-tag:hover { background: #0056b3; transform: scale(1.05); }
        .filter-tag .remove-btn { width: 16px; height: 16px; border-radius: 50%; background: rgba(255,255,255,0.3); border: none; color: white; font-size: 10px; cursor: pointer; display: flex; align-items: center; justify-content: center; transition: all 0.2s; }
        .filter-tag .remove-btn:hover { background: rgba(255,255,255,0.5); transform: scale(1.1); }
        .filter-tag.vendor-tag { background: #34C759; }
        .filter-tag.vendor-tag:hover { background: #28a745; }
        .filter-tag.time-tag { background: #FF9500; }
        .filter-tag.time-tag:hover { background: #e6850e; }
        .filter-tag.name-tag { background: #5856D6; }
        .filter-tag.name-tag:hover { background: #4a4a9e; }
        .filter-tag.genre-tag { background: #FF3B30; }
        .filter-tag.genre-tag:hover { background: #d63333; }
        .filter-tag.popularity-tag { background: #AF52DE; }
        .filter-tag.popularity-tag:hover { background: #8e44ad; }
        .filter-tag.no-filter { background: #999; }
        .filter-tag.no-filter:hover { background: #777; }
        .no-filters { font-size: 12px; color: #999; font-style: italic; }
        .vendor-header { padding: 15px; background: #f8f8f8; cursor: pointer; display: flex; justify-content: space-between; align-items: center; }
        .vendor-header:hover { background: #f0f0f0; }
        .vendor-header .vendor-title { display: flex; align-items: center; gap: 8px; }
        .vendor-header .vendor-name { font-size: 16px; font-weight: 600; color: #333; }
        .vendor-header .game-count { font-size: 14px; color: #666; }
        .vendor-header .toggle-icon { font-size: 20px; color: #666; transition: transform 0.3s; }
        .vendor-header.collapsed .toggle-icon { transform: rotate(-90deg); }
        .vendor-content { transition: max-height 0.3s ease-out; overflow: hidden; }
        .vendor-content.collapsed { max-height: 0 !important; }
        .game-list { min-height: 200px; }
        
        /* 人气筛选样式 */
        .popularity-filter-section label {
            border: 1px solid #ddd;
            transition: all 0.2s;
        }
        .popularity-filter-section label:hover {
            background: #e8e8e8 !important;
            border-color: #007AFF;
        }
        .popularity-filter-section input[type="radio"]:checked + span {
            color: #007AFF;
            font-weight: 600;
        }
        .popularity-filter-section input[type="radio"]:checked {
            accent-color: #007AFF;
        }
        .popularity-filter-section label:has(input[type="radio"]:checked) {
            background: #e6f3ff !important;
            border-color: #007AFF;
        }
        
        /* 游戏类型筛选模式样式 */
        .tag-filter-mode label {
            transition: all 0.2s;
        }
        .tag-filter-mode label:hover {
            transform: scale(1.05);
        }
        .tag-filter-mode input[type="radio"]:checked + span {
            font-weight: 600;
        }
        .tag-filter-mode input[type="radio"][value="include"]:checked + span {
            color: #007AFF;
        }
        .tag-filter-mode input[type="radio"][value="exclude"]:checked + span {
            color: #FF3B30;
        }
        .tag-filter-mode label:has(input[type="radio"][value="include"]:checked) {
            background: #e6f3ff !important;
            border-color: #007AFF;
        }
        .tag-filter-mode label:has(input[type="radio"][value="exclude"]:checked) {
            background: #ffe6e6 !important;
            border-color: #FF3B30;
        }
    </style>
</head>
<body>
    <!-- 加载进度显示 -->
    <div id="loading-progress" style="
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-align: center;
        font-size: 18px;
        color: #666;
        z-index: 1000;
    ">
        正在加载数据...
    </div>

    <!-- 主要内容容器，初始隐藏 -->
    <div class="container" style="display: none;">
        <h1>🎮 iOS游戏库 (2025/09/10)</h1>
    <div id="view-mode-bar" class="view-mode-buttons" style="margin: 20px 0;">
        <button class="view-mode-button active" data-mode="icon">📱 图标模式</button>
        <button class="view-mode-button" data-mode="vendor">🏢 厂商分类</button>
    </div>
    <div id="filter-conditions" class="filter-conditions">
        <div style="display: flex; align-items: center; flex-wrap: wrap;">
            <span class="filter-conditions-title">筛选条件：</span>
            <div id="filter-tags" class="filter-tags"></div>
        </div>
    </div>
    <div id="stats-info" class="stats-info">
        <div class="stats-controls-row">
            <div class="stats-text">显示 0 款游戏</div>
            <div class="sort-controls">
                <div class="sort-order-control">
                    <button id="sort-order-btn" class="sort-order-btn" title="切换排序方向">
                        <span id="sort-order-icon">↓</span>
                        <span id="sort-order-text">降序</span>
                    </button>
                </div>
                <div class="sort-type-dropdown">
                    <button id="sort-type-btn" class="sort-type-btn">
                        <span id="sort-type-text">厂商排名</span>
                        <span class="dropdown-arrow">▼</span>
                    </button>
                    <div id="sort-type-content" class="sort-type-content">
                        <div class="sort-type-option" data-sort="vendor_rank">厂商排名</div>
                        <div class="sort-type-option" data-sort="rating_count">评价数</div>
                        <div class="sort-type-option" data-sort="rating">评分</div>
                        <div class="sort-type-option" data-sort="release_date">上架时间</div>
                        <div class="sort-type-option" data-sort="update_date">更新时间</div>
                        <div class="sort-type-option" data-sort="import_time">入库时间</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div id="game-list"></div>
    <div id="loading-spinner" class="loading-spinner"></div>
    <button id="load-more" class="load-more-button">加载更多</button>
    <div id="filter-toggle" class="filter-toggle">🔍</div>
    <div id="filter-panel" class="filter-panel">
        <div class="filter-content">
            <div class="filter-header">
                <div class="filter-title">游戏筛选</div>
                <button class="filter-close">&times;</button>
            </div>
            <div class="filter-section name-filter-section">
                <div class="filter-section-title">游戏名称</div>
                <input type="text" id="name-filter" class="name-filter-input" placeholder="输入游戏名称或产品ID搜索...">
            </div>
            <div class="filter-section">
                <div class="vendor-section-header">
                    <div class="filter-section-title">时间筛选</div>
                    <div class="vendor-controls">
                        <button id="clear-time-range" style="padding: 6px 12px; background: #666; color: white; border: none; border-radius: 4px; font-size: 12px; cursor: pointer;">清除</button>
                    </div>
                </div>
                <div class="time-filter-section">
                    <div style="display: flex; gap: 10px; align-items: center; font-size: 14px;">
                        <select id="time-filter-type" style="padding: 6px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px; background: white; cursor: pointer;">
                            <option value="release">上架时间</option>
                            <option value="update">更新时间</option>
                            <option value="import_time" selected>入库时间</option>
                            <option value="delete_time">删除时间</option>
                        </select>
                        <input type="date" id="time-start-date" class="date-input" style="padding: 6px; border: 1px solid #ddd; border-radius: 4px; font-size: 12px;">
                        <span>至</span>
                        <input type="date" id="time-end-date" class="date-input" style="padding: 6px; border: 1px solid #ddd; border-radius: 4px; font-size: 12px;">
                    </div>
                </div>
            </div>
            <div class="filter-section">
                <div class="vendor-section-header">
                    <div class="filter-section-title">人气筛选</div>
                    <div class="vendor-controls">
                        <button id="clear-popularity-filter" style="padding: 6px 12px; background: #666; color: white; border: none; border-radius: 4px; font-size: 12px; cursor: pointer;">清除</button>
                    </div>
                </div>
                <div class="popularity-filter-section">
                    <div style="display: flex; flex-wrap: wrap; gap: 8px; font-size: 14px;">
                        <label style="display: flex; align-items: center; gap: 6px; cursor: pointer; padding: 6px 12px; border-radius: 6px; background: #f5f5f5; transition: all 0.2s;">
                            <input type="radio" name="popularity-filter" value="all" checked style="margin: 0;">
                            <span>全部</span>
                        </label>
                        <label style="display: flex; align-items: center; gap: 6px; cursor: pointer; padding: 6px 12px; border-radius: 6px; background: #f5f5f5; transition: all 0.2s;">
                            <input type="radio" name="popularity-filter" value="lt100" style="margin: 0;">
                            <span>小于1百</span>
                        </label>
                        <label style="display: flex; align-items: center; gap: 6px; cursor: pointer; padding: 6px 12px; border-radius: 6px; background: #f5f5f5; transition: all 0.2s;">
                            <input type="radio" name="popularity-filter" value="gt1000" style="margin: 0;">
                            <span>大于1千</span>
                        </label>
                        <label style="display: flex; align-items: center; gap: 6px; cursor: pointer; padding: 6px 12px; border-radius: 6px; background: #f5f5f5; transition: all 0.2s;">
                            <input type="radio" name="popularity-filter" value="gt10000" style="margin: 0;">
                            <span>大于1万</span>
                        </label>
                        <label style="display: flex; align-items: center; gap: 6px; cursor: pointer; padding: 6px 12px; border-radius: 6px; background: #f5f5f5; transition: all 0.2s;">
                            <input type="radio" name="popularity-filter" value="gt50000" style="margin: 0;">
                            <span>大于5万</span>
                        </label>
                        <label style="display: flex; align-items: center; gap: 6px; cursor: pointer; padding: 6px 12px; border-radius: 6px; background: #f5f5f5; transition: all 0.2s;">
                            <input type="radio" name="popularity-filter" value="gt100000" style="margin: 0;">
                            <span>大于10万</span>
                        </label>
                        <label style="display: flex; align-items: center; gap: 6px; cursor: pointer; padding: 6px 12px; border-radius: 6px; background: #f5f5f5; transition: all 0.2s;">
                            <input type="radio" name="popularity-filter" value="gt1000000" style="margin: 0;">
                            <span>大于1百万</span>
                        </label>
                    </div>
                </div>
            </div>
            <div class="filter-section">
                <div class="vendor-section-header">
                    <div class="filter-section-title">游戏类型</div>
                    <div class="vendor-controls">
                        <div class="tag-filter-mode" style="display: flex; gap: 8px; margin-right: 8px;">
                            <label style="display: flex; align-items: center; gap: 4px; cursor: pointer; font-size: 12px; padding: 4px 8px; border-radius: 4px; background: #e6f3ff; border: 1px solid #007AFF;">
                                <input type="radio" name="tag-filter-mode" value="include" checked style="margin: 0; accent-color: #007AFF;">
                                <span style="color: #007AFF; font-weight: 600;">包含</span>
                            </label>
                            <label style="display: flex; align-items: center; gap: 4px; cursor: pointer; font-size: 12px; padding: 4px 8px; border-radius: 4px; background: #f5f5f5; border: 1px solid #ddd;">
                                <input type="radio" name="tag-filter-mode" value="exclude" style="margin: 0; accent-color: #FF3B30;">
                                <span style="color: #666;">排除</span>
                            </label>
                        </div>
                        <button id="select-all-tags" class="select-top100-button" style="background: #007AFF;">全选</button>
                        <button id="invert-tags" class="select-top100-button" style="background: #FF9500;">反选</button>
                    </div>
                </div>
                <div class="tag-filter-grid" id="tag-filter-grid"></div>
            </div>
            <div class="filter-section">
                <div class="vendor-section-header">
                    <div class="filter-section-title">厂商</div>
                    <div class="vendor-controls">
                        <div class="sort-dropdown">
                            <button class="sort-dropdown-btn">综合排序</button>
                            <div class="sort-dropdown-content">
                                <div class="sort-option" data-sort="comprehensive">综合排序</div>
                                <div class="sort-option" data-sort="total_rating_count">总评分人数</div>
                                <div class="sort-option" data-sort="high_rating_products">成功产品数</div>
                            </div>
                        </div>
                        <div class="vendor-select-dropdown">
                            <button class="vendor-select-btn" onclick="toggleVendorSelectDropdown()">选择前N名 ▼</button>
                            <div class="vendor-select-content">
                                <div class="vendor-select-option" data-count="10" onclick="selectTopVendors(10)">选择前10</div>
                                <div class="vendor-select-option" data-count="50" onclick="selectTopVendors(50)">选择前50</div>
                                <div class="vendor-select-option" data-count="100" onclick="selectTopVendors(100)">选择前100</div>
                                <div class="vendor-select-option active" data-count="200" onclick="selectTopVendors(200)">选择前200</div>
                                <div class="vendor-select-option" data-count="300" onclick="selectTopVendors(300)">选择前300</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="vendor-search-section" style="margin-bottom: 12px;">
                    <input type="text" id="vendor-search" class="name-filter-input" placeholder="输入厂商名称搜索..." style="margin-bottom: 8px;">
                </div>
                <div class="tag-filter-grid" id="vendor-filter-grid"></div>
            </div>
            <div class="filter-actions">
                <button class="filter-apply" style="background: #34C759;">应用筛选</button>
            </div>
        </div>
    </div>
        <div id="screenshotOverlay" class="screenshot-overlay">
            <div class="screenshot-container"></div>
            <button class="screenshot-close">×</button>
        </div>
    </div> <!-- 关闭主要内容容器 -->

    <script>// 游戏类型中英文对照表
const GAME_GENRE_TRANSLATIONS = {
    'Action': '动作',
    'Adventure': '冒险',
    'Arcade': '街机',
    'Art': '艺术',
    'Animals & Nature': '动物与自然',
    'Board': '棋牌',
    'Books': '图书',
    'Business': '商业',
    'Card': '卡牌',
    'Casino': '赌场',
    'Casual': '休闲',
    'Comics & Cartoons': '漫画与卡通',
    'Cooking': '烹饪',
    'Developer Tools': '开发工具',
    'Education': '教育',
    'Emoji & Expressions': '表情与表达',
    'Entertainment': '娱乐',
    'Family': '家庭',
    'Finance': '金融',
    'Food & Drink': '饮食',
    'Gaming': '游戏',
    'Graphics & Design': '图形与设计',
    'Health & Fitness': '健康健身',
    'Kids & Family': '儿童与家庭',
    'Lifestyle': '生活方式',
    'Magazines & Newspapers': '杂志报纸',
    'Medical': '医疗',
    'Movies & TV': '影视',
    'Music': '音乐',
    'Navigation': '导航',
    'News': '新闻',
    'Photo & Video': '照片与视频',
    'Places & Objects': '地点与物品',
    'Productivity': '效率工具',
    'Puzzle': '益智',
    'Racing': '竞速',
    'Reference': '参考资料',
    'Role Playing': '角色扮演',
    'Shopping': '购物',
    'Simulation': '模拟',
    'Social Networking': '社交网络',
    'Sports': '体育',
    'Stickers': '贴纸',
    'Strategy': '策略',
    'Survival': '生存',
    'Time Management': '时间管理',
    'Tools': '工具',
    'Travel': '旅行',
    'Trivia': '问答',
    'Utilities': '实用工具',
    'Weather': '天气',
    'Word': '文字',
    'RPG': '角色扮演',
    'FPS': '第一人称射击',
    'MMO': '大型多人在线',
    'MOBA': '多人在线战术竞技',
    'Sandbox': '沙盒',
    'Horror': '恐怖',
    'Comedy': '喜剧',
    'Drama': '剧情',
    'Fantasy': '奇幻',
    'Sci-Fi': '科幻',
    'War': '战争',
    'Historical': '历史',
    'Mystery': '悬疑',
    'Thriller': '惊悚',
    'Romance': '恋爱',
    'Western': '西部',
    'Martial Arts': '武术',
    'Stealth': '潜行',
    'Tower Defense': '塔防',
    'Match-3': '三消',
    'Hidden Object': '找茬',
    'Farming': '农场',
    'City Building': '城市建设',
    'Law': '法律',
    'Politics': '政治',
    'Religion': '宗教',
    'Photography': '摄影',
    'Video': '视频',
    'Social Networking': '社交网络',
    'Lifestyle': '生活方式',
    'Health & Fitness': '健康健身',
    'Travel': '旅行',
    'Navigation': '导航',
    'Weather': '天气',
    'News': '新闻',
    'Magazines & Newspapers': '杂志报纸',
    'Shopping': '购物',
    'Finance': '金融',
    'Productivity': '效率工具',
    'Utilities': '实用工具',
    'Developer Tools': '开发工具',
    'Reference': '参考资料',
    'Medical': '医疗',
    'Catalogs': '目录',
    'Food & Drink': '饮食',
    'Games': '游戏'
};

// 全局变量
let appsData = {};
let filteredApps = [];
let currentPage = 1;
let itemsPerPage = 50;
let itemsPerRow = 6;
let isLoading = false;
let currentViewMode = 'icon';
let nameFilter = '';
let timeFilterType = 'import_time';
let timeDateRange = { start: null, end: null };
let popularityFilter = 'all'; // 人气筛选：all, lt100, gt1000, gt10000, gt50000, gt100000, gt1000000
let selectedTags = new Set();
let tagFilterMode = 'include'; // 游戏类型筛选模式：include(包含), exclude(排除)
let selectedVendors = new Set();
let vendorSearchFilter = '';
let vendorSortMode = 'comprehensive';

// 应用排序相关变量
let appSortType = 'vendor_rank'; // 排序类型：vendor_rank, rating_count, rating, release_date, update_date, import_time
let appSortOrder = 'desc'; // 排序方向：asc, desc

// 暂存的筛选条件（用于复位）
let tempTimeFilterType = timeFilterType;
let tempTimeDateRange = { start: timeDateRange.start, end: timeDateRange.end };
let tempNameFilter = nameFilter;
let tempPopularityFilter = popularityFilter;
let tempSelectedTags = new Set(selectedTags);
let tempTagFilterMode = tagFilterMode;
let tempSelectedVendors = new Set(selectedVendors);
let tempVendorSortMode = vendorSortMode;

// 数据清单和常量
const DATA_MANIFEST = {"chunk_files":["apps_chunk_000.json","apps_chunk_001.json","apps_chunk_002.json","apps_chunk_003.json","apps_chunk_004.json","apps_chunk_005.json","apps_chunk_006.json","apps_chunk_007.json","apps_chunk_008.json","apps_chunk_009.json"],"vendors_file":"main_vendors.json","stats_file":"vendor_stats.json","total_chunks":10};
const APP_PREFIX = "https://apps.apple.com/us/app/";
const DEV_PREFIX = "https://apps.apple.com/developer/";

// 全局数据变量（重新初始化）
appsData = {};
let MAIN_VENDORS = [];
let VENDOR_STATS = {};
let dataLoadingProgress = 0;
let totalDataFiles = 0;

// 异步加载JSON文件的函数
async function loadJsonFile(filename) {
    try {
        const response = await fetch(`data/${filename}`);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return await response.json();
    } catch (error) {
        console.error(`Error loading ${filename}:`, error);
        throw error;
    }
}

// 更新加载进度
function updateLoadingProgress() {
    dataLoadingProgress++;
    const percentage = Math.round((dataLoadingProgress / totalDataFiles) * 100);
    const loadingElement = document.getElementById('loading-progress');
    if (loadingElement) {
        loadingElement.textContent = `加载数据中... ${percentage}%`;
    }
}

// 显示加载错误
function showLoadingError(error) {
    const loadingElement = document.getElementById('loading-progress');
    if (loadingElement) {
        loadingElement.innerHTML = `
            <div style="color: red;">
                数据加载失败: ${error.message}<br>
                请检查网络连接或刷新页面重试
            </div>
        `;
    }
}

// 异步加载所有数据
async function loadAllData() {
    try {
        // 计算总文件数
        totalDataFiles = DATA_MANIFEST.chunk_files.length + 2; // +2 for vendors and stats

        // 显示加载进度
        const loadingElement = document.getElementById('loading-progress');
        if (loadingElement) {
            loadingElement.textContent = '开始加载数据...';
        }

        // 并行加载主要厂商和统计数据
        const [vendorsData, statsData] = await Promise.all([
            loadJsonFile(DATA_MANIFEST.vendors_file),
            loadJsonFile(DATA_MANIFEST.stats_file)
        ]);

        MAIN_VENDORS = vendorsData;
        VENDOR_STATS = statsData;
        updateLoadingProgress();
        updateLoadingProgress();

        // 分批加载应用数据块，避免一次性加载太多文件
        const batchSize = 5; // 每批加载5个文件
        for (let i = 0; i < DATA_MANIFEST.chunk_files.length; i += batchSize) {
            const batch = DATA_MANIFEST.chunk_files.slice(i, i + batchSize);
            const batchPromises = batch.map(filename => loadJsonFile(filename));
            const batchResults = await Promise.all(batchPromises);

            // 合并批次数据
            batchResults.forEach((chunkData, index) => {
                Object.assign(appsData, chunkData);
                updateLoadingProgress();
            });

            // 给UI一点时间更新
            await new Promise(resolve => setTimeout(resolve, 10));
        }

        // 数据加载完成，初始化界面
        initializeInterface();

    } catch (error) {
        console.error('Failed to load data:', error);
        showLoadingError(error);
    }
}

// 辅助函数：从带分数的厂商名称中提取纯厂商名称
function extractVendorName(vendorWithScore) {
    if (typeof vendorWithScore !== 'string') return vendorWithScore;
    // 匹配格式如 "Voodoo(3727)" 并提取 "Voodoo"
    const match = vendorWithScore.match(/^(.+?)\(\d+\)$/);
    return match ? match[1] : vendorWithScore;
}

// 创建纯厂商名称列表用于匹配（在数据加载后创建）
let MAIN_VENDOR_NAMES = [];

// 辅助函数：获取厂商的统计信息显示文本
function getVendorStatsDisplay(vendorName) {
    const stats = VENDOR_STATS[vendorName];
    if (!stats) return '';

    const successRate = stats.success_rate.toFixed(0);
    const totalRatingCount = stats.total_rating_count.toLocaleString();
    return `${totalRatingCount}\n${successRate}%(${stats.high_rating_products})`;
}

// 厂商排序方式
const SORT_MODES = {
    'total_rating_count': '总评分人数',
    'high_rating_products': '成功产品数',
    'comprehensive': '综合排序'
};

// 预计算的排名数据（避免重复计算）
let precomputedRankings = null;
// 预排序的厂商列表（避免重复排序）
let presortedVendors = null;

// 分页配置
const ROWS_PER_PAGE = 10; // 每页显示10行

// 设置默认时间范围（最近7天）
const today = new Date();
const sevenDaysAgo = new Date();
sevenDaysAgo.setDate(today.getDate() - 7);

// 设置开始时间为7天前的0:00:00
sevenDaysAgo.setHours(0, 0, 0, 0);
// 设置结束时间为今天的23:59:59
today.setHours(23, 59, 59, 999);

timeDateRange.start = sevenDaysAgo;
timeDateRange.end = today;

// 设置输入框的值
const timeStartDate = document.getElementById('time-start-date');
const timeEndDate = document.getElementById('time-end-date');
if (timeStartDate && timeEndDate) {
    // 使用本地时间格式化日期
    const formatDateForInput = (date) => {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    };
    
    timeStartDate.value = formatDateForInput(sevenDaysAgo);
    timeEndDate.value = formatDateForInput(today);
}

// DOM加载完成后的初始化函数
function init() {
    // 显示加载进度
    const loadingElement = document.getElementById('loading-progress');
    if (loadingElement) {
        loadingElement.textContent = '正在初始化...';
    }

    // 开始加载数据
    loadAllData();
}

// 数据加载完成后的初始化函数
function initializeInterface() {
    // 隐藏加载进度，显示主界面
    const loadingElement = document.getElementById('loading-progress');
    if (loadingElement) {
        loadingElement.style.display = 'none';
    }

    // 显示主要内容
    const mainContent = document.querySelector('.container');
    if (mainContent) {
        mainContent.style.display = 'block';
    }

    // 创建纯厂商名称列表用于匹配
    MAIN_VENDOR_NAMES = MAIN_VENDORS.map(extractVendorName);

    // 初始化暂存变量
    tempTimeFilterType = timeFilterType;
    tempTimeDateRange = { start: timeDateRange.start, end: timeDateRange.end };
    tempNameFilter = nameFilter;
    tempPopularityFilter = popularityFilter;
    tempSelectedTags = new Set(selectedTags);
    tempTagFilterMode = tagFilterMode;
    tempSelectedVendors = new Set(selectedVendors);

    setupEventListeners();
    processInitialData();

    // 应用初始排序
    sortApps();

    // 设置默认的厂商选择：综合排序前200名
    const { sortedVendors } = precomputeRankings();
    const top200Vendors = sortedVendors.comprehensive.slice(0, 200);

    top200Vendors.forEach(vendor => {
        selectedVendors.add(vendor);
        tempSelectedVendors.add(vendor);
    });

    // 更新厂商选择按钮的默认状态
    updateVendorSelectButton(200);

    renderFilters();
    calculateItemsPerRow();
    loadAndRenderApps(true);
    updateStats();

    // 监听窗口大小变化
    window.addEventListener('resize', debounce(() => {
        const oldItemsPerRow = itemsPerRow;
        calculateItemsPerRow();
        // 只有当每行显示数量改变时才重新渲染
        if (oldItemsPerRow !== itemsPerRow) {
            loadAndRenderApps(true);
        }
    }, 250));
}

// 设置事件监听器
function setupEventListeners() {
    document.querySelectorAll('.view-mode-button').forEach(button => {
        button.addEventListener('click', () => {
            currentViewMode = button.dataset.mode;
            document.querySelectorAll('.view-mode-button').forEach(b => b.classList.remove('active'));
            button.classList.add('active');
            currentPage = 1;
            loadAndRenderApps(true);
        });
    });

    const filterToggle = document.getElementById('filter-toggle');
    const filterPanel = document.getElementById('filter-panel');
    const filterClose = document.querySelector('.filter-close');
    const filterApply = document.querySelector('.filter-apply');

    // 打开筛选面板时，保存当前筛选条件到暂存变量
    filterToggle.addEventListener('click', () => {
        // 保存当前筛选条件
        tempTimeFilterType = timeFilterType;
        tempTimeDateRange = { start: timeDateRange.start, end: timeDateRange.end };
        tempNameFilter = nameFilter;
        tempPopularityFilter = popularityFilter;
        tempSelectedTags = new Set(selectedTags);
        tempTagFilterMode = tagFilterMode;
        tempSelectedVendors = new Set(selectedVendors);
        tempVendorSortMode = vendorSortMode;
        
        // 更新UI显示当前筛选条件
        updateFilterUI();
        
        filterPanel.classList.add('active');
    });

    // 关闭筛选面板时，复位到之前的筛选条件
    filterClose.addEventListener('click', () => {
        // 复位筛选条件
        resetFilterConditions();
        
        filterPanel.classList.remove('active');
    });

    // 应用筛选时，确认筛选条件并关闭面板
    filterApply.addEventListener('click', () => {
        // 确认所有暂存的筛选条件
        confirmFilterConditions();
        
        filterPanel.classList.remove('active');
        currentPage = 1;
        loadAndRenderApps(true);
    });

    const loadMoreButton = document.getElementById('load-more');
    loadMoreButton.addEventListener('click', () => {
        if (!isLoading) {
            currentPage++;
            loadAndRenderApps(false);
        }
    });

    // 添加滚动事件监听
    window.addEventListener('scroll', scrollHandler);

    // 添加名称筛选输入框事件监听（改为暂存模式）
    const nameFilterInput = document.getElementById('name-filter');
    nameFilterInput.addEventListener('input', debounce(() => {
        tempNameFilter = nameFilterInput.value.toLowerCase().trim();
    }, 300));

    // 添加厂商搜索输入框事件监听
    const vendorSearchInput = document.getElementById('vendor-search');
    vendorSearchInput.addEventListener('input', debounce(() => {
        vendorSearchFilter = vendorSearchInput.value.toLowerCase().trim();
        renderVendorFilters(); // 重新渲染厂商筛选器
    }, 300));

    // 添加时间筛选事件监听
    const clearTimeRangeBtn = document.getElementById('clear-time-range');
    const timeTypeSelect = document.getElementById('time-filter-type');
    const timeStartDate = document.getElementById('time-start-date');
    const timeEndDate = document.getElementById('time-end-date');

    // 时间类型选择器事件监听
    timeTypeSelect.addEventListener('change', () => {
        tempTimeFilterType = timeTypeSelect.value;
    });

    // 开始日期输入框事件监听
    timeStartDate.addEventListener('change', () => {
        const startDate = timeStartDate.value;
        
        // 开始日期设置为当天的0:00:00（本地时间）
        if (startDate) {
            // 解析日期字符串为本地时间
            const [year, month, day] = startDate.split('-').map(Number);
            const startDateTime = new Date(year, month - 1, day, 0, 0, 0, 0);
            tempTimeDateRange.start = startDateTime;
        } else {
            tempTimeDateRange.start = null;
        }
    });

    // 结束日期输入框事件监听
    timeEndDate.addEventListener('change', () => {
        const endDate = timeEndDate.value;
        
        // 结束日期设置为当天的23:59:59（本地时间）
        if (endDate) {
            // 解析日期字符串为本地时间
            const [year, month, day] = endDate.split('-').map(Number);
            const endDateTime = new Date(year, month - 1, day, 23, 59, 59, 999);
            tempTimeDateRange.end = endDateTime;
        } else {
            tempTimeDateRange.end = null;
        }
    });

    clearTimeRangeBtn.addEventListener('click', () => {
        // 只清除暂存变量，不立即生效
        tempTimeDateRange.start = null;
        tempTimeDateRange.end = null;
        timeStartDate.value = '';
        timeEndDate.value = '';
        
        showToast('时间筛选已清除，请点击"应用筛选"按钮生效');
    });

    // 添加人气筛选事件监听
    const clearPopularityFilterBtn = document.getElementById('clear-popularity-filter');
    const popularityFilterInputs = document.querySelectorAll('input[name="popularity-filter"]');

    // 人气筛选单选按钮事件监听
    popularityFilterInputs.forEach(input => {
        input.addEventListener('change', () => {
            tempPopularityFilter = input.value;
        });
    });

    // 清除人气筛选按钮事件监听
    clearPopularityFilterBtn.addEventListener('click', () => {
        // 只清除暂存变量，不立即生效
        tempPopularityFilter = 'all';
        document.querySelector('input[name="popularity-filter"][value="all"]').checked = true;
        
        showToast('人气筛选已清除，请点击"应用筛选"按钮生效');
    });

    // 添加排序下拉菜单事件监听器
    const sortDropdown = document.querySelector('.sort-dropdown');
    const sortDropdownBtn = document.querySelector('.sort-dropdown-btn');
    const sortOptions = document.querySelectorAll('.sort-option');

    // 点击下拉按钮切换显示状态
    sortDropdownBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        sortDropdown.classList.toggle('active');
    });

    // 点击排序选项
    sortOptions.forEach(option => {
        option.addEventListener('click', () => {
            const newSortMode = option.dataset.sort;
            if (newSortMode !== tempVendorSortMode) {
                tempVendorSortMode = newSortMode;

                // 更新选项的活跃状态
                sortOptions.forEach(opt => opt.classList.remove('active'));
                option.classList.add('active');

                // 更新排序按钮显示文字
                updateSortButtonText();

                // 只重新渲染厂商筛选器，保持用户当前的选择不变
                // 排序模式变化不会自动重新选择厂商
                renderVendorFilters();
            }
            sortDropdown.classList.remove('active');
        });
    });

    // 点击其他地方关闭下拉菜单
    document.addEventListener('click', () => {
        sortDropdown.classList.remove('active');
    });

    // 初始化排序选项的活跃状态
    sortOptions.forEach(option => {
        if (option.dataset.sort === tempVendorSortMode) {
            option.classList.add('active');
        }
    });
    
    // 初始化排序按钮文字
    updateSortButtonText();

    // 初始化应用排序控件
    initializeAppSortControls();

    // 添加游戏类型全选按钮事件监听器
    const selectAllTagsBtn = document.getElementById('select-all-tags');
    selectAllTagsBtn.addEventListener('click', () => {
        const tagGrid = document.getElementById('tag-filter-grid');
        const allTagButton = tagGrid.querySelector('[data-tag="all"]');
        const tagButtons = tagGrid.querySelectorAll('.tag-filter-item:not([data-tag="all"])');
        
        // 清除"全部"选项
        allTagButton.classList.remove('selected');
        allTagButton.querySelector('input[type="checkbox"]').checked = false;
        
        // 选中所有游戏类型
        tagButtons.forEach(item => {
            item.classList.add('selected');
            item.querySelector('input[type="checkbox"]').checked = true;
            const tag = item.dataset.tag;
            tempSelectedTags.add(tag);
        });
        
        showToast('已全选所有游戏类型');
    });

    // 添加游戏类型反选按钮事件监听器
    const invertTagsBtn = document.getElementById('invert-tags');
    invertTagsBtn.addEventListener('click', () => {
        const tagGrid = document.getElementById('tag-filter-grid');
        const allTagButton = tagGrid.querySelector('[data-tag="all"]');
        const tagButtons = tagGrid.querySelectorAll('.tag-filter-item:not([data-tag="all"])');
        
        // 清除"全部"选项
        allTagButton.classList.remove('selected');
        allTagButton.querySelector('input[type="checkbox"]').checked = false;
        
        // 反选所有游戏类型
        tagButtons.forEach(item => {
            const tag = item.dataset.tag;
            const checkbox = item.querySelector('input[type="checkbox"]');
            
            if (tempSelectedTags.has(tag)) {
                // 如果已选中，则取消选中
                item.classList.remove('selected');
                checkbox.checked = false;
                tempSelectedTags.delete(tag);
            } else {
                // 如果未选中，则选中
                item.classList.add('selected');
                checkbox.checked = true;
                tempSelectedTags.add(tag);
            }
        });
        
        // 如果没有选中任何类型，则选中"全部"
        if (tempSelectedTags.size === 0) {
            allTagButton.classList.add('selected');
            allTagButton.querySelector('input[type="checkbox"]').checked = true;
        }
        
        showToast('已反选游戏类型');
    });

    // 添加游戏类型筛选模式切换事件监听器
    const tagFilterModeInputs = document.querySelectorAll('input[name="tag-filter-mode"]');
    tagFilterModeInputs.forEach(input => {
        input.addEventListener('change', (e) => {
            tempTagFilterMode = e.target.value;
            
            // 更新样式
            const labels = document.querySelectorAll('.tag-filter-mode label');
            labels.forEach(label => {
                const radio = label.querySelector('input[type="radio"]');
                if (radio.value === tempTagFilterMode) {
                    label.style.background = radio.value === 'include' ? '#e6f3ff' : '#ffe6e6';
                    label.style.borderColor = radio.value === 'include' ? '#007AFF' : '#FF3B30';
                } else {
                    label.style.background = '#f5f5f5';
                    label.style.borderColor = '#ddd';
                }
            });
            
            showToast(`已切换到${tempTagFilterMode === 'include' ? '包含' : '排除'}模式`);
        });
    });
}

// 检查是否接近页面底部
function isNearBottom() {
    const gameList = document.getElementById('game-list');
    const loadMoreButton = document.getElementById('load-more');
    
    // 如果没有更多内容可加载，禁用滚动加载
    if (!loadMoreButton.classList.contains('show')) {
        return false;
    }
    
    return window.innerHeight + window.scrollY >= document.documentElement.scrollHeight - 1000;
}

// 处理初始数据
function processInitialData() {
    filteredApps = [];
    for (const [vendor, apps] of Object.entries(appsData)) {
        for (const [appId, appInfo] of Object.entries(apps)) {
            // 获取开发者信息
            let developerName = vendor;
            if (appInfo.developer && appInfo.developer.name) {
                developerName = appInfo.developer.name;
            }
            filteredApps.push({
                id: appId,
                vendor: developerName,
                ...appInfo
            });
        }
    }
    
    // 使用新的排序功能
    sortApps();
}

// 渲染所有筛选器
function renderFilters() {
    renderTagFilters();
    renderVendorFilters();
}

// 渲染标签筛选器
function renderTagFilters() {
    const tagSet = new Set();
    filteredApps.forEach(app => {
        (app.genres || []).forEach(tag => tagSet.add(tag));
    });

    // 将游戏类型按字母顺序排序
    const sortedTags = Array.from(tagSet).sort((a, b) => a.localeCompare(b, 'zh-CN'));

    const tagGrid = document.getElementById('tag-filter-grid');
    tagGrid.innerHTML = `
        <div class="tag-filter-item" data-tag="all">
            <label class="filter-label">
                <input type="checkbox" id="tag-all">
                <span>全部</span>
            </label>
        </div>
        ${sortedTags.map(tag => {
            const displayName = GAME_GENRE_TRANSLATIONS[tag] || tag;
            return `
                <div class="tag-filter-item" data-tag="${tag}">
                    <label class="filter-label">
                        <input type="checkbox" id="tag-${tag}">
                        <span>${displayName}</span>
                    </label>
                </div>
            `;
        }).join('')}
    `;

    const allTagButton = tagGrid.querySelector('[data-tag="all"]');
    const tagButtons = tagGrid.querySelectorAll('.tag-filter-item:not([data-tag="all"])');

    // 修改事件监听器，使用 change 事件
    allTagButton.querySelector('input[type="checkbox"]').addEventListener('change', (e) => {
        allTagButton.classList.toggle('selected', e.target.checked);
        if (e.target.checked) {
            tagButtons.forEach(item => {
                item.classList.remove('selected');
                item.querySelector('input[type="checkbox"]').checked = false;
            });
            tempSelectedTags.clear();
        }
    });

    tagButtons.forEach(item => {
        const checkbox = item.querySelector('input[type="checkbox"]');
        checkbox.addEventListener('change', (e) => {
            const tag = item.dataset.tag;
            item.classList.toggle('selected', e.target.checked);
            if (e.target.checked) {
                tempSelectedTags.add(tag);
                allTagButton.classList.remove('selected');
                allTagButton.querySelector('input[type="checkbox"]').checked = false;
            } else {
                tempSelectedTags.delete(tag);
                if (tempSelectedTags.size === 0) {
                    allTagButton.classList.add('selected');
                    allTagButton.querySelector('input[type="checkbox"]').checked = true;
                }
            }
        });
    });

    // 根据暂存变量设置选中状态
    if (tempSelectedTags.size === 0) {
        allTagButton.classList.add('selected');
        allTagButton.querySelector('input[type="checkbox"]').checked = true;
    } else {
        allTagButton.classList.remove('selected');
        allTagButton.querySelector('input[type="checkbox"]').checked = false;
        tagButtons.forEach(item => {
            const tag = item.dataset.tag;
            if (tempSelectedTags.has(tag)) {
                item.classList.add('selected');
                item.querySelector('input[type="checkbox"]').checked = true;
            }
        });
    }
}

// 预计算所有排名数据和排序列表（只计算一次，避免重复计算）
function precomputeRankings() {
    if (precomputedRankings && presortedVendors) {
        return { rankings: precomputedRankings, sortedVendors: presortedVendors };
    }

    const allVendors = Object.keys(VENDOR_STATS);
    const rankings = {
        total_rating_count: {},
        high_rating_products: {},
        comprehensive: {}
    };

    // 计算总评分人数排名和排序列表
    const ratingRanking = [...allVendors].sort((a, b) => {
        return VENDOR_STATS[b].total_rating_count - VENDOR_STATS[a].total_rating_count;
    });
    ratingRanking.forEach((vendor, index) => {
        rankings.total_rating_count[vendor] = index + 1;
    });

    // 计算成功产品数排名和排序列表
    const productRanking = [...allVendors].sort((a, b) => {
        return VENDOR_STATS[b].high_rating_products - VENDOR_STATS[a].high_rating_products;
    });
    productRanking.forEach((vendor, index) => {
        rankings.high_rating_products[vendor] = index + 1;
    });

    // 计算综合排名（基于前两个排名的和）
    const comprehensiveRanking = [...allVendors].sort((a, b) => {
        const aScore = rankings.total_rating_count[a] + rankings.high_rating_products[a];
        const bScore = rankings.total_rating_count[b] + rankings.high_rating_products[b];
        return aScore - bScore;
    });
    comprehensiveRanking.forEach((vendor, index) => {
        rankings.comprehensive[vendor] = index + 1;
    });

    // 预排序的厂商列表
    presortedVendors = {
        total_rating_count: ratingRanking,
        high_rating_products: productRanking,
        comprehensive: comprehensiveRanking
    };

    precomputedRankings = rankings;
    return { rankings, sortedVendors: presortedVendors };
}

// 渲染厂商筛选器
function renderVendorFilters() {
    const vendorGrid = document.getElementById('vendor-filter-grid');

    // 获取预计算的排名数据和预排序列表
    const { rankings, sortedVendors } = precomputeRankings();

    // 直接使用预排序的厂商列表，避免重复排序
    const allVendors = sortedVendors[tempVendorSortMode];

    // 使用预计算的排名映射表
    const vendorRankMap = rankings[tempVendorSortMode];

    // 如果有搜索条件，则筛选厂商列表
    let filterVendors = allVendors;
    if (vendorSearchFilter) {
        filterVendors = allVendors.filter(vendor =>
            vendor.toLowerCase().includes(vendorSearchFilter)
        );
    }

    vendorGrid.innerHTML = `
        <div class="tag-filter-item" data-vendor="all">
            <span class="vendor-display">
                <div class="vendor-header-row">
                    <div class="vendor-rank">全部</div>
                    <input type="checkbox" id="vendor-all">
                </div>
            </span>
        </div>
        ${filterVendors.map(vendor => {
            const stats = VENDOR_STATS[vendor];
            if (!stats) return '';

            const totalRatingCount = stats.total_rating_count.toLocaleString();
            const successRate = stats.success_rate.toFixed(0);
            const rank = vendorRankMap[vendor]; // 使用真实排名

            return `
                <div class="tag-filter-item ${tempSelectedVendors.has(vendor) ? 'selected' : ''}" data-vendor="${vendor}" title="${vendor}">
                    <span class="vendor-display">
                        <div class="vendor-header-row">
                            <div class="vendor-rank">No.${rank}</div>
                            <input type="checkbox" id="vendor-${vendor.replace(/[^a-zA-Z0-9]/g, '-')}" ${tempSelectedVendors.has(vendor) ? 'checked' : ''}>
                        </div>
                        <div class="vendor-name" title="${vendor}">${vendor}</div>
                        <div class="vendor-total-rating">人气: ${totalRatingCount}</div>
                        <div class="vendor-success-rate">成功率: ${successRate}%(${stats.high_rating_products})</div>
                    </span>
                </div>
            `;
        }).join('')}
    `;

    const allVendorButton = vendorGrid.querySelector('[data-vendor="all"]');
    const vendorButtons = vendorGrid.querySelectorAll('.tag-filter-item:not([data-vendor="all"])');

    // 修改事件监听器，使用 change 事件
    allVendorButton.querySelector('input[type="checkbox"]').addEventListener('change', (e) => {
        allVendorButton.classList.toggle('selected', e.target.checked);
        if (e.target.checked) {
            vendorButtons.forEach(item => {
                item.classList.remove('selected');
                item.querySelector('input[type="checkbox"]').checked = false;
            });
            tempSelectedVendors.clear();
            // 当选择全部时，更新按钮状态为"全部"
            updateVendorSelectButton(0);
        }
    });
    
    // 为"全部"按钮添加点击事件，支持点击任意位置选中
    allVendorButton.addEventListener('click', (e) => {
        // 如果点击的不是复选框本身，则切换复选框状态
        if (e.target.type !== 'checkbox') {
            const checkbox = allVendorButton.querySelector('input[type="checkbox"]');
            checkbox.checked = !checkbox.checked;
            checkbox.dispatchEvent(new Event('change'));
        }
    });

    vendorButtons.forEach(item => {
        const checkbox = item.querySelector('input[type="checkbox"]');
        checkbox.addEventListener('change', (e) => {
            const vendor = item.dataset.vendor;
            item.classList.toggle('selected', e.target.checked);
            if (e.target.checked) {
                tempSelectedVendors.add(vendor);
                allVendorButton.classList.remove('selected');
                allVendorButton.querySelector('input[type="checkbox"]').checked = false;
            } else {
                tempSelectedVendors.delete(vendor);
                if (tempSelectedVendors.size === 0) {
                    allVendorButton.classList.add('selected');
                    allVendorButton.querySelector('input[type="checkbox"]').checked = true;
                }
            }
            
            // 当手动选择厂商时，更新按钮状态
            updateVendorSelectButton(tempSelectedVendors.size);
        });
        
        // 添加点击整个卡片的点击事件
        item.addEventListener('click', (e) => {
            // 如果点击的不是复选框本身，则切换复选框状态
            if (e.target.type !== 'checkbox') {
                checkbox.checked = !checkbox.checked;
                checkbox.dispatchEvent(new Event('change'));
            }
        });
    });

    // 根据暂存变量设置选中状态
    if (tempSelectedVendors.size === 0) {
        allVendorButton.classList.add('selected');
        allVendorButton.querySelector('input[type="checkbox"]').checked = true;
    } else {
        allVendorButton.classList.remove('selected');
        allVendorButton.querySelector('input[type="checkbox"]').checked = false;
        
        // 确保所有选中的厂商都正确显示选中状态
        vendorButtons.forEach(item => {
            const vendor = item.dataset.vendor;
            const checkbox = item.querySelector('input[type="checkbox"]');
            if (tempSelectedVendors.has(vendor)) {
                item.classList.add('selected');
                checkbox.checked = true;
            } else {
                item.classList.remove('selected');
                checkbox.checked = false;
            }
        });
    }
}

// 计算每行可以显示多少个项目
function calculateItemsPerRow() {
    const container = document.querySelector('.app-grid') || document.createElement('div');
    container.style.display = 'flex';
    container.style.flexWrap = 'wrap';
    container.style.gap = '16px';
    container.style.padding = '16px';
    document.body.appendChild(container);

    const testItem = document.createElement('div');
    testItem.className = 'app-item icon-mode';
    container.appendChild(testItem);

    const itemWidth = testItem.offsetWidth + 16; // 包括间距
    const containerWidth = container.offsetWidth;
    
    // 计算每行可以容纳的项目数
    itemsPerRow = Math.floor(containerWidth / itemWidth);
    
    // 清理测试元素
    if (container.parentNode === document.body) {
        document.body.removeChild(container);
    }
}

// 更新统计信息
function updateStats() {
    const filteredResults = filterApps();
    const statsInfo = document.getElementById('stats-info');
    const filterConditions = document.getElementById('filter-conditions');
    const filterTags = document.getElementById('filter-tags');
    
    let statsText = `显示 ${filteredResults.length} 款游戏`;
    
    // 收集筛选条件
    const filterConditionsList = [];
    
    // 游戏名称筛选（支持游戏名称和产品ID）
    if (nameFilter) {
        // 检测是否为ID格式（纯数字或id+数字）
        const isIdFormat = /^(id)?\d+$/.test(nameFilter);
        const displayLabel = isIdFormat ? `产品ID: ${nameFilter}` : `游戏名称: ${nameFilter}`;
        
        filterConditionsList.push({
            type: 'name',
            label: displayLabel,
            value: nameFilter
        });
    }
    
    // 时间筛选
    if (timeDateRange.start || timeDateRange.end) {
        const timeTypeNames = {
            'release': '上架时间',
            'update': '更新时间', 
            'import_time': '入库时间',
            'delete_time': '删除时间'
        };
        const timeTypeName = timeTypeNames[timeFilterType] || '时间';
        const startDate = timeDateRange.start ? timeDateRange.start.toLocaleDateString() : '不限';
        const endDate = timeDateRange.end ? timeDateRange.end.toLocaleDateString() : '不限';
        filterConditionsList.push({
            type: 'time',
            label: `${timeTypeName}: ${startDate} - ${endDate}`,
            value: { type: timeFilterType, start: timeDateRange.start, end: timeDateRange.end }
        });
    }
    
    // 人气筛选
    if (popularityFilter !== 'all') {
        const popularityLabels = {
            'lt100': '小于1百',
            'gt1000': '大于1千',
            'gt10000': '大于1万',
            'gt50000': '大于5万',
            'gt100000': '大于10万',
            'gt1000000': '大于1百万'
        };
        filterConditionsList.push({
            type: 'popularity',
            label: `人气: ${popularityLabels[popularityFilter]}`,
            value: popularityFilter
        });
    }
    
    // 游戏类型筛选
    if (selectedTags.size > 0) {
        const tagLabels = Array.from(selectedTags).map(tag => {
            return GAME_GENRE_TRANSLATIONS[tag] || tag;
        });
        const modeText = tagFilterMode === 'include' ? '包含' : '排除';
        filterConditionsList.push({
            type: 'genre',
            label: `游戏类型(${modeText}): ${tagLabels.join(', ')}`,
            value: Array.from(selectedTags)
        });
    }
    
    // 厂商筛选
    if (selectedVendors.size > 0) {
        const vendorLabels = Array.from(selectedVendors).map(vendor => {
            return extractVendorName(vendor);
        });
        // 新的显示格式：厂商:100个(xxxxxxxxxx....)
        let displayText = vendorLabels.join(',');
        if (displayText.length > 50) {
            displayText = displayText.substring(0, 47) + '...';
        }
        filterConditionsList.push({
            type: 'vendor',
            label: `厂商:${selectedVendors.size}个(${displayText})`,
            value: Array.from(selectedVendors)
        });
    }
    
    // 更新筛选条件显示
    updateFilterConditionsDisplay(filterConditionsList);
    
    // 更新统计信息文本
    const statsTextElement = statsInfo.querySelector('.stats-text');
    if (statsTextElement) {
        statsTextElement.textContent = statsText;
    }
}

// 更新筛选条件显示
function updateFilterConditionsDisplay(conditions) {
    const filterConditions = document.getElementById('filter-conditions');
    const filterTags = document.getElementById('filter-tags');
    
    // 清空现有标签
    filterTags.innerHTML = '';
    
    // 检查是否有筛选条件
    if (conditions.length === 0) {
        filterConditions.style.display = 'none';
        return;
    }
    
    // 有筛选条件时显示区域
    filterConditions.style.display = 'block';
    
    // 创建筛选标签
    conditions.forEach(condition => {
        const tag = document.createElement('div');
        tag.className = `filter-tag ${condition.type}-tag`;
        
        const label = document.createElement('span');
        label.textContent = condition.label;
        tag.appendChild(label);
        
        // 添加移除按钮
        const removeBtn = document.createElement('button');
        removeBtn.className = 'remove-btn';
        removeBtn.innerHTML = '×';
        removeBtn.onclick = (e) => {
            e.stopPropagation();
            removeFilterCondition(condition.type);
        };
        tag.appendChild(removeBtn);
        
        filterTags.appendChild(tag);
    });
}

// 移除筛选条件
function removeFilterCondition(type) {
    switch (type) {
        case 'name':
            nameFilter = '';
            tempNameFilter = '';
            document.getElementById('name-filter').value = '';
            break;
        case 'time':
            timeDateRange.start = null;
            timeDateRange.end = null;
            tempTimeDateRange.start = null;
            tempTimeDateRange.end = null;
            document.getElementById('time-start-date').value = '';
            document.getElementById('time-end-date').value = '';
            break;
        case 'genre':
            selectedTags.clear();
            tempSelectedTags.clear();
            // 重置模式为包含模式
            tagFilterMode = 'include';
            tempTagFilterMode = 'include';
            // 更新模式选择器的UI
            const includeRadio = document.querySelector('input[name="tag-filter-mode"][value="include"]');
            const excludeRadio = document.querySelector('input[name="tag-filter-mode"][value="exclude"]');
            if (includeRadio) includeRadio.checked = true;
            if (excludeRadio) excludeRadio.checked = false;
            // 更新样式
            const labels = document.querySelectorAll('.tag-filter-mode label');
            labels.forEach(label => {
                const radio = label.querySelector('input[type="radio"]');
                if (radio.value === 'include') {
                    label.style.background = '#e6f3ff';
                    label.style.borderColor = '#007AFF';
                } else {
                    label.style.background = '#f5f5f5';
                    label.style.borderColor = '#ddd';
                }
            });
            // 清除所有标签的选中状态
            const tagButtons = document.querySelectorAll('#tag-filter-grid .tag-filter-item');
            tagButtons.forEach(item => {
                item.classList.remove('selected');
                const checkbox = item.querySelector('input[type="checkbox"]');
                if (checkbox) checkbox.checked = false;
            });
            break;
        case 'vendor':
            selectedVendors.clear();
            tempSelectedVendors.clear();
            // 清除所有厂商的选中状态
            const vendorButtons = document.querySelectorAll('#vendor-filter-grid .tag-filter-item');
            vendorButtons.forEach(item => {
                item.classList.remove('selected');
                const checkbox = item.querySelector('input[type="checkbox"]');
                if (checkbox) checkbox.checked = false;
            });
            break;
        case 'popularity':
            popularityFilter = 'all';
            tempPopularityFilter = 'all';
            // 清除人气筛选的选中状态
            const popularityInputs = document.querySelectorAll('input[name="popularity-filter"]');
            popularityInputs.forEach(input => {
                if (input.value === 'all') {
                    input.checked = true;
                } else {
                    input.checked = false;
                }
            });
            break;
    }
    
    // 重新加载应用
    currentPage = 1;
    loadAndRenderApps(true);
}

// 筛选应用
function filterApps() {
    return filteredApps.filter(app => {
        // 名称筛选（支持游戏名称和产品ID）
        if (nameFilter) {
            const appName = app.name.toLowerCase();
            const appId = (app.id || '').toLowerCase();
            const filterValue = nameFilter.toLowerCase();
            
            // 检查是否匹配游戏名称或产品ID
            if (!appName.includes(filterValue) && !appId.includes(filterValue)) {
                return false;
            }
        }

        // 时间范围筛选
        if (timeDateRange.start || timeDateRange.end) {
            let targetDate = null;
            
            // 根据选择的时间类型获取相应的日期
            switch (timeFilterType) {
                case 'release':
                    targetDate = new Date(app['Released on']);
                    break;
                case 'update':
                    targetDate = new Date(app['Updated on']);
                    break;
                case 'import_time':
                    targetDate = app['import_time'] ? new Date(app['import_time']) : null;
                    break;
                case 'delete_time':
                    targetDate = app['delete_time'] ? new Date(app['delete_time']) : null;
                    break;
                default:
                    targetDate = new Date(app['Released on']);
            }
            
            // 如果目标日期无效，跳过这个应用
            if (!targetDate || isNaN(targetDate.getTime())) {
                return false;
        }

            if (timeDateRange.start && targetDate < timeDateRange.start) {
                return false;
            }
            if (timeDateRange.end && targetDate > timeDateRange.end) {
                return false;
            }
        }

        // 人气筛选（基于评分人数）
        if (popularityFilter !== 'all') {
            const ratingCount = parseInt(app['Rating Count'] || '0');
            
            switch (popularityFilter) {
                case 'lt100':
                    if (ratingCount >= 100) return false;
                    break;
                case 'gt1000':
                    if (ratingCount < 1000) return false;
                    break;
                case 'gt10000':
                    if (ratingCount < 10000) return false;
                    break;
                case 'gt50000':
                    if (ratingCount < 50000) return false;
                    break;
                case 'gt100000':
                    if (ratingCount < 100000) return false;
                    break;
                case 'gt1000000':
                    if (ratingCount < 1000000) return false;
                    break;
            }
        }

        // 标签筛选
        if (selectedTags.size > 0) {
            const appTags = app.genres || [];
            
            if (tagFilterMode === 'include') {
                // 包含模式：只要包含任意一个选中的标签就显示
                if (!Array.from(selectedTags).some(tag => appTags.includes(tag))) {
                    return false;
                }
            } else if (tagFilterMode === 'exclude') {
                // 排除模式：包含任意一个选中的标签就不显示
                if (Array.from(selectedTags).some(tag => appTags.includes(tag))) {
                    return false;
                }
            }
        }

        // 厂商筛选（模糊匹配）
        if (selectedVendors.size > 0) {
            const appVendor = (app.developer && app.developer.name) || app.vendor || '';

            // 检查是否选中了"其他"选项
            if (selectedVendors.has("其他")) {
                // 检查该应用的开发者是否不在主要厂商列表中
                const isOtherVendor = !MAIN_VENDOR_NAMES.some(vendor =>
                    vendor.toLowerCase().includes(appVendor.toLowerCase()) ||
                    appVendor.toLowerCase().includes(vendor.toLowerCase())
                );

                // 如果只选择了"其他"，则只显示其他厂商的应用
                if (selectedVendors.size === 1) {
                    return isOtherVendor;
                }

                // 如果同时选择了"其他"和其他厂商，则显示两者的应用
                return isOtherVendor || Array.from(selectedVendors).some(vendor =>
                    vendor !== "其他" && (
                        extractVendorName(vendor).toLowerCase().includes(appVendor.toLowerCase()) ||
                        appVendor.toLowerCase().includes(extractVendorName(vendor).toLowerCase())
                    )
                );
            }
            
            // 如果没有选中"其他"，则只显示选中厂商的应用
            return Array.from(selectedVendors).some(vendor =>
                extractVendorName(vendor).toLowerCase().includes(appVendor.toLowerCase()) ||
                appVendor.toLowerCase().includes(extractVendorName(vendor).toLowerCase())
            );
        }

        return true;
    });
}

// 修改加载和渲染应用的函数
function loadAndRenderApps(reset = false) {
    if (isLoading) return;
    isLoading = true;

    const loadingSpinner = document.getElementById('loading-spinner');
    const loadMoreButton = document.getElementById('load-more');
    const gameList = document.getElementById('game-list');
    
    loadingSpinner.classList.add('active');

    if (reset) {
        gameList.innerHTML = '';
        currentPage = 1;
    }

    const filteredResults = filterApps();
    
    // 应用排序到筛选后的结果
    sortApps(filteredResults);
    
    updateStats();

    if (currentViewMode === 'vendor') {
        // 按厂商分组
        const vendorGroups = {};
        filteredResults.forEach(app => {
            if (!vendorGroups[app.vendor]) {
                vendorGroups[app.vendor] = [];
            }
            vendorGroups[app.vendor].push(app);
        });

        // 按游戏数量排序厂商（在厂商模式下，厂商的排序仍然按游戏数量）
        const sortedVendors = Object.entries(vendorGroups)
            .sort(([,a], [,b]) => b.length - a.length);

        // 计算分页
        const TARGET_PAGE_SIZE = 50;
        const MAX_VENDORS_PER_PAGE = 4;
        const MIN_GAMES_FOR_NEW_PAGE = 40;

        const pages = [];
        let currentPageVendors = [];
        let currentPageGames = 0;

        for (const [vendor, apps] of sortedVendors) {
            const vendorGames = apps.length;

            if (vendorGames > TARGET_PAGE_SIZE) {
                if (currentPageVendors.length > 0) {
                    pages.push(currentPageVendors);
                    currentPageVendors = [];
                    currentPageGames = 0;
                }
                pages.push([[vendor, apps]]);
                continue;
            }

            if (currentPageVendors.length >= MAX_VENDORS_PER_PAGE || 
                (currentPageGames >= MIN_GAMES_FOR_NEW_PAGE && currentPageVendors.length > 0)) {
                pages.push(currentPageVendors);
                currentPageVendors = [];
                currentPageGames = 0;
            }

            currentPageVendors.push([vendor, apps]);
            currentPageGames += vendorGames;
        }

        if (currentPageVendors.length > 0) {
            pages.push(currentPageVendors);
        }

        if (currentPage <= pages.length) {
            const pageVendors = pages[currentPage - 1];
            for (const [vendor, apps] of pageVendors) {
                const vendorSection = document.createElement('div');
                vendorSection.className = 'vendor-section';
                const developerUrl = apps[0].developer?.url || `${DEV_PREFIX}${vendor}`;
                const vendorId = `vendor-${vendor.replace(/[^a-zA-Z0-9]/g, '-')}`;
                
                // 检查是否已经存在折叠状态
                const isCollapsed = localStorage.getItem(vendorId) === 'collapsed';
                
                vendorSection.innerHTML = `
                    <div class="vendor-header ${isCollapsed ? 'collapsed' : ''}" data-vendor-id="${vendorId}">
                        <div class="vendor-title">
                            <span class="vendor-name">${vendor}</span>
                            <span class="game-count">（${apps.length}款游戏）</span>
                        </div>
                        <span class="toggle-icon">${isCollapsed ? '▶' : '▼'}</span>
                    </div>
                    <div class="vendor-content ${isCollapsed ? 'collapsed' : ''}" style="max-height: ${isCollapsed ? '0' : '9999px'}">
                        <div class="app-grid">
                            ${apps.map(app => renderAppItem(app)).join('')}
                        </div>
                    </div>
                `;
                
                // 添加折叠/展开事件监听
                const header = vendorSection.querySelector('.vendor-header');
                header.addEventListener('click', () => {
                    const content = vendorSection.querySelector('.vendor-content');
                    const toggleIcon = header.querySelector('.toggle-icon');
                    const isCurrentlyCollapsed = header.classList.contains('collapsed');
                    
                    // 切换折叠状态
                    header.classList.toggle('collapsed');
                    content.classList.toggle('collapsed');
                    
                    // 更新图标
                    toggleIcon.textContent = isCurrentlyCollapsed ? '▼' : '▶';
                    
                    // 设置内容高度
                    if (!isCurrentlyCollapsed) {
                        content.style.maxHeight = '0';
                    } else {
                        content.style.maxHeight = '9999px';
                    }
                    
                    // 保存状态到localStorage
                    localStorage.setItem(vendorId, isCurrentlyCollapsed ? 'expanded' : 'collapsed');
                });
                
                gameList.appendChild(vendorSection);
            }
        }

        loadMoreButton.classList.toggle('show', currentPage < pages.length);
    } else {
        // 图标模式
        const itemsPerPage = itemsPerRow * ROWS_PER_PAGE;
        const startIndex = (currentPage - 1) * itemsPerPage;
        const endIndex = startIndex + itemsPerPage;
        const pageItems = filteredResults.slice(startIndex, endIndex);

        const container = document.createElement('div');
        container.className = 'app-grid';
        container.innerHTML = pageItems.map(app => renderAppItem(app)).join('');
        gameList.appendChild(container);

        // 更新加载更多按钮的显示状态
        const hasMore = endIndex < filteredResults.length;
        loadMoreButton.classList.toggle('show', hasMore);
        
        // 如果没有更多内容，移除滚动监听
        if (!hasMore) {
            window.removeEventListener('scroll', scrollHandler);
        }
    }

    loadingSpinner.classList.remove('active');
    isLoading = false;
}

// 创建一个防抖的滚动处理函数
const scrollHandler = debounce(() => {
    if (isNearBottom() && !isLoading) {
        currentPage++;
        loadAndRenderApps(false);
    }
}, 200);

// 渲染单个应用项
function renderAppItem(app) {
    const ratingMatch = app.Stat ? app.Stat.match(/([\d.]+)\s*([★☆]+)\s*\((\d+)\)/) : null;
    const rating = ratingMatch ? {
        score: ratingMatch[1],
        stars: ratingMatch[2],
        count: ratingMatch[3]
    } : null;

    const developerUrl = app.developer?.url || `${DEV_PREFIX}${app.vendor}`;
    const developerName = app.vendor;
    const hasFullScreenshots = app.screenshotUrls && app.screenshotUrls.length >= 5;
    
    // 安全处理截图URLs
    const safeScreenshots = app.screenshotUrls ? app.screenshotUrls.map(url => 
        url.replace(/[\\"']/g, '\\$&').replace(/\u0000/g, '\\0')
    ) : [];
    
    // 检查是否为删除的游戏
    const isDeleted = app['delete_time'] ? true : false;
    
    // 确保app.id存在，如果不存在则使用一个默认值
    const appId = app.id || 'unknown';
    
    // 获取app的详情链接，优先使用trackViewUrl，如果没有则使用默认链接
    const appDetailUrl = app['trackViewUrl'] || `${APP_PREFIX}${appId}`;
    
    // 构建时间显示内容
    let timeDisplay = '';
    
    // 始终显示上架时间和更新时间
    timeDisplay += `<div class="app-date">上架: ${formatDate(app['Released on'])}</div>`;
    timeDisplay += `<div class="app-date">更新: ${formatDate(app['Updated on'])}</div>`;
    
    // 如果有入库时间，也显示
    if (app['import_time']) {
        timeDisplay += `<div class="app-date">入库: ${formatDate(app['import_time'])}</div>`;
    }
    
    // 如果有删除时间，也显示（但用不同颜色标识）
    if (app['delete_time']) {
        timeDisplay += `<div class="app-date" style="color: #ff3b30;">删除: ${formatDate(app['delete_time'])}</div>`;
    }
    
    // 根据是否删除设置游戏名和卡片的样式
    const appNameStyle = isDeleted ? 'style="color: #ff3b30; text-decoration: line-through;"' : '';
    const cardStyle = isDeleted ? 'style="background-color: #fff5f5; border-color: #ffcdd2;"' : '';
    
    return `
        <div class="app-item icon-mode" ${cardStyle}>
            <img class="app-icon ${hasFullScreenshots ? 'has-screenshots' : ''}" 
                 src="${app['Icon URL']}" 
                 alt="${app.name}" 
                 onclick='handleScreenshots(${JSON.stringify(appId)}, ${JSON.stringify(safeScreenshots)})' />
            <div class="app-info">
                <a href="${appDetailUrl}" target="_blank" class="app-name" ${appNameStyle}>${app.name}</a>
                <div class="app-id">id${appId}</div>
                <a href="${developerUrl}" target="_blank" class="developer-name">${developerName}</a>
                ${rating ? `
                    <div class="rating">
                        <span class="rating-stars">${rating.stars}</span>
                        <span class="rating-count">(${rating.count})</span>
                    </div>
                ` : ''}
                <div class="app-dates">
                    ${timeDisplay}
                </div>
                ${app.genres && app.genres.length > 0 ? `
                    <div class="game-tags">
                        ${app.genres.map(tag => {
                            const displayName = GAME_GENRE_TRANSLATIONS[tag] || tag;
                            return `<span class="game-tag">${displayName}</span>`;
                        }).join('')}
                    </div>
                ` : ''}
            </div>
        </div>
    `;
}

// 处理截图显示的函数
function handleScreenshots(appId, screenshots) {
    if (!screenshots || !Array.isArray(screenshots) || screenshots.length === 0) {
        showToast('没有可用的截图');
        return;
    }
    showScreenshots(appId, screenshots);
}

// 显示截图
function showScreenshots(appId, screenshots) {
    const overlay = document.getElementById('screenshotOverlay');
    const container = overlay.querySelector('.screenshot-container');
    
    container.innerHTML = `
        <div class="screenshot-grid">
            ${screenshots.map(url => `
                <img src="${url}" alt="Screenshot" onerror="this.style.display='none'">
            `).join('')}
        </div>
    `;
    
    // 显示弹窗
    overlay.classList.add('active');
    
    // 阻止滚动传播
    const grid = container.querySelector('.screenshot-grid');
    grid.addEventListener('wheel', (e) => {
        e.stopPropagation();
        
        // 如果内容已经滚动到边缘，则不阻止默认行为
        const isAtStart = grid.scrollLeft === 0;
        const isAtEnd = grid.scrollLeft + grid.clientWidth >= grid.scrollWidth;
        
        if ((isAtStart && e.deltaX < 0) || (isAtEnd && e.deltaX > 0)) {
            return;
        }
        
        e.preventDefault();
        grid.scrollLeft += e.deltaX + e.deltaY;
    });

    // 关闭按钮事件
    const closeScreenshots = () => {
        overlay.classList.remove('active');
        document.removeEventListener('keydown', handleEscKey);
    };

    // ESC键关闭事件
    const handleEscKey = (e) => {
        if (e.key === 'Escape') {
            closeScreenshots();
        }
    };

    // 添加ESC键监听
    document.addEventListener('keydown', handleEscKey);
    
    // 点击关闭按钮
    overlay.querySelector('.screenshot-close').onclick = closeScreenshots;
    
    // 点击背景关闭
    overlay.onclick = (e) => {
        if (e.target === overlay) {
            closeScreenshots();
        }
    };

    // 防止点击截图时关闭
    container.onclick = (e) => {
        e.stopPropagation();
    };
}

// 显示提示消息
function showToast(message) {
    const toast = document.createElement('div');
    toast.className = 'toast-message';
    toast.textContent = message;
    document.body.appendChild(toast);
    
    setTimeout(() => {
        toast.classList.add('show');
    }, 100);
    
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 300);
    }, 2000);
}

// 格式化日期
function formatDate(dateStr) {
    if (!dateStr) return '未知';
    const date = new Date(dateStr);
    if (isNaN(date.getTime())) return '未知';
    return `${date.getFullYear()}/${(date.getMonth() + 1).toString().padStart(2, '0')}/${date.getDate().toString().padStart(2, '0')}`;
}

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 切换厂商选择下拉菜单
function toggleVendorSelectDropdown() {
    const dropdown = document.querySelector('.vendor-select-dropdown');
    dropdown.classList.toggle('active');
    
    // 点击其他地方关闭下拉菜单
    document.addEventListener('click', function closeDropdown(e) {
        if (!dropdown.contains(e.target)) {
            dropdown.classList.remove('active');
            document.removeEventListener('click', closeDropdown);
        }
    });
}

// 选中前N个厂商的功能（暂存模式，需要应用筛选后才生效）
function selectTopVendors(count) {
    // 获取预排序的厂商列表
    const { sortedVendors } = precomputeRankings();

    // 根据当前排序模式来选择前N个厂商
    const topVendors = sortedVendors[tempVendorSortMode].slice(0, count);

    // 追加选中前N个厂商，不清除之前的选择
    topVendors.forEach(vendor => {
        tempSelectedVendors.add(vendor);
    });

    // 更新按钮文本和选中状态
    updateVendorSelectButton(tempSelectedVendors.size);
    
    // 关闭下拉菜单
    document.querySelector('.vendor-select-dropdown').classList.remove('active');

    // 重新渲染厂商筛选器（显示暂存状态）
    renderVendorFilters();
    
    // 显示提示信息
    showToast(`已追加选择前${count}个厂商，共选中${tempSelectedVendors.size}个厂商，请点击"应用筛选"按钮生效`);
}

// 更新排序按钮显示文字
function updateSortButtonText() {
    const button = document.querySelector('.sort-dropdown-btn');
    if (button) {
        const sortText = SORT_MODES[tempVendorSortMode] || '综合排序';
        button.textContent = sortText;
    }
}

// 初始化应用排序控件
function initializeAppSortControls() {
    const sortOrderBtn = document.getElementById('sort-order-btn');
    const sortTypeBtn = document.getElementById('sort-type-btn');
    const sortTypeDropdown = document.querySelector('.sort-type-dropdown');
    const sortTypeOptions = document.querySelectorAll('.sort-type-option');
    
    // 排序方向按钮事件
    if (sortOrderBtn) {
        sortOrderBtn.addEventListener('click', () => {
            appSortOrder = appSortOrder === 'desc' ? 'asc' : 'desc';
            updateSortOrderUI();
            currentPage = 1;
            loadAndRenderApps(true);
        });
    }
    
    // 排序类型下拉按钮事件
    if (sortTypeBtn) {
        sortTypeBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            sortTypeDropdown.classList.toggle('active');
        });
    }
    
    // 排序类型选项事件
    sortTypeOptions.forEach(option => {
        option.addEventListener('click', () => {
            const newSortType = option.dataset.sort;
            if (newSortType !== appSortType) {
                appSortType = newSortType;
                updateSortTypeUI();
                currentPage = 1;
                loadAndRenderApps(true);
            }
            sortTypeDropdown.classList.remove('active');
        });
    });
    
    // 点击其他地方关闭下拉菜单
    document.addEventListener('click', () => {
        sortTypeDropdown.classList.remove('active');
    });
    
    // 初始化UI状态
    updateSortOrderUI();
    updateSortTypeUI();
}

// 更新排序方向UI
function updateSortOrderUI() {
    const sortOrderBtn = document.getElementById('sort-order-btn');
    const sortOrderIcon = document.getElementById('sort-order-icon');
    const sortOrderText = document.getElementById('sort-order-text');
    
    if (sortOrderBtn && sortOrderIcon && sortOrderText) {
        if (appSortOrder === 'asc') {
            sortOrderBtn.classList.add('asc');
            sortOrderIcon.textContent = '↑';
            sortOrderText.textContent = '升序';
        } else {
            sortOrderBtn.classList.remove('asc');
            sortOrderIcon.textContent = '↓';
            sortOrderText.textContent = '降序';
        }
    }
}

// 更新排序类型UI
function updateSortTypeUI() {
    const sortTypeBtn = document.getElementById('sort-type-btn');
    const sortTypeText = document.getElementById('sort-type-text');
    const sortTypeOptions = document.querySelectorAll('.sort-type-option');
    
    if (sortTypeBtn && sortTypeText) {
        const sortTypeLabels = {
            'vendor_rank': '厂商排名',
            'rating_count': '评价数',
            'rating': '评分',
            'release_date': '上架时间',
            'update_date': '更新时间',
            'import_time': '入库时间'
        };
        sortTypeText.textContent = sortTypeLabels[appSortType] || '厂商排名';
    }
    
    // 更新选项的活跃状态
    sortTypeOptions.forEach(option => {
        if (option.dataset.sort === appSortType) {
            option.classList.add('active');
        } else {
            option.classList.remove('active');
        }
    });
}

// 排序应用列表
function sortApps(appsArray = filteredApps) {
    const { rankings, sortedVendors } = precomputeRankings();
    const vendorRankMap = rankings[vendorSortMode];
    
    appsArray.sort((a, b) => {
        let comparison = 0;
        
        switch (appSortType) {
            case 'vendor_rank':
                // 按厂商排名排序
                const vendorA = a.vendor;
                const vendorB = b.vendor;
                const rankA = vendorRankMap[vendorA] || Number.MAX_SAFE_INTEGER;
                const rankB = vendorRankMap[vendorB] || Number.MAX_SAFE_INTEGER;
                comparison = rankA - rankB;
                break;
                
            case 'rating_count':
                // 按评价数排序
                const countA = parseInt(a['Rating Count'] || '0');
                const countB = parseInt(b['Rating Count'] || '0');
                comparison = countA - countB;
                break;
                
            case 'rating':
                // 按评分排序
                const ratingA = parseFloat(a['Rating'] || '0');
                const ratingB = parseFloat(b['Rating'] || '0');
                comparison = ratingA - ratingB;
                break;
                
            case 'release_date':
                // 按上架时间排序
                const releaseA = new Date(a['Released on'] || '1970-01-01');
                const releaseB = new Date(b['Released on'] || '1970-01-01');
                comparison = releaseA - releaseB;
                break;
                
            case 'update_date':
                // 按更新时间排序
                const updateA = new Date(a['Updated on'] || '1970-01-01');
                const updateB = new Date(b['Updated on'] || '1970-01-01');
                comparison = updateA - updateB;
                break;
                
            case 'import_time':
                // 按入库时间排序
                const importA = new Date(a['import_time'] || '1970-01-01');
                const importB = new Date(b['import_time'] || '1970-01-01');
                comparison = importA - importB;
                break;
                
            default:
                comparison = 0;
        }
        
        // 根据排序方向调整
        return appSortOrder === 'asc' ? comparison : -comparison;
    });
}

// 更新厂商选择按钮的文本和选中状态
function updateVendorSelectButton(selectedCount) {
    const button = document.querySelector('.vendor-select-btn');
    const options = document.querySelectorAll('.vendor-select-option');
    
    if (button) {
        // 按钮文本保持固定，不根据选中数量变化
        button.textContent = `选择前N名 ▼`;
    }
    
    // 更新选中状态（这个逻辑可以保留，用于高亮显示当前选中的选项）
    options.forEach(option => {
        const count = parseInt(option.dataset.count);
        if (count === selectedCount) {
            option.classList.add('active');
        } else {
            option.classList.remove('active');
        }
    });
}

// 选中前100厂商的功能（保持兼容性）
function selectTop100Vendors() {
    selectTopVendors(200);
}

// 初始化
document.addEventListener('DOMContentLoaded', init);

// 更新筛选UI显示当前筛选条件
function updateFilterUI() {
    // 更新时间筛选UI
    const timeTypeSelect = document.getElementById('time-filter-type');
    const timeStartDate = document.getElementById('time-start-date');
    const timeEndDate = document.getElementById('time-end-date');
    const nameFilterInput = document.getElementById('name-filter');
    
    if (timeTypeSelect) timeTypeSelect.value = tempTimeFilterType;
    
    // 修复时区问题：使用本地时间而不是UTC时间
    if (timeStartDate) {
        if (tempTimeDateRange.start) {
            const year = tempTimeDateRange.start.getFullYear();
            const month = String(tempTimeDateRange.start.getMonth() + 1).padStart(2, '0');
            const day = String(tempTimeDateRange.start.getDate()).padStart(2, '0');
            timeStartDate.value = `${year}-${month}-${day}`;
        } else {
            timeStartDate.value = '';
        }
    }
    
    if (timeEndDate) {
        if (tempTimeDateRange.end) {
            const year = tempTimeDateRange.end.getFullYear();
            const month = String(tempTimeDateRange.end.getMonth() + 1).padStart(2, '0');
            const day = String(tempTimeDateRange.end.getDate()).padStart(2, '0');
            timeEndDate.value = `${year}-${month}-${day}`;
        } else {
            timeEndDate.value = '';
        }
    }
    
    if (nameFilterInput) nameFilterInput.value = tempNameFilter;
    
    // 更新人气筛选UI
    const popularityFilterInputs = document.querySelectorAll('input[name="popularity-filter"]');
    popularityFilterInputs.forEach(input => {
        if (input.value === tempPopularityFilter) {
            input.checked = true;
        } else {
            input.checked = false;
        }
    });
    
    // 更新游戏类型筛选模式选择器
    const tagFilterModeInputs = document.querySelectorAll('input[name="tag-filter-mode"]');
    tagFilterModeInputs.forEach(input => {
        if (input.value === tempTagFilterMode) {
            input.checked = true;
            // 更新样式
            const label = input.closest('label');
            if (label) {
                if (input.value === 'include') {
                    label.style.background = '#e6f3ff';
                    label.style.borderColor = '#007AFF';
                } else {
                    label.style.background = '#ffe6e6';
                    label.style.borderColor = '#FF3B30';
                }
            }
        } else {
            input.checked = false;
            // 更新样式
            const label = input.closest('label');
            if (label) {
                label.style.background = '#f5f5f5';
                label.style.borderColor = '#ddd';
            }
        }
    });
    
    // 更新标签和厂商筛选器（在renderTagFilters和renderVendorFilters中处理）
    renderTagFilters();
    renderVendorFilters();
    
    // 更新厂商选择按钮状态
    const currentSelectedCount = tempSelectedVendors.size;
    updateVendorSelectButton(currentSelectedCount);
    
    // 更新排序按钮文字
    updateSortButtonText();
}

// 复位筛选条件到之前的状态
function resetFilterConditions() {
    // 复位暂存变量到当前生效的筛选条件
    tempTimeFilterType = timeFilterType;
    tempTimeDateRange = { start: timeDateRange.start, end: timeDateRange.end };
    tempNameFilter = nameFilter;
    tempPopularityFilter = popularityFilter;
    tempSelectedTags = new Set(selectedTags);
    tempTagFilterMode = tagFilterMode;
    tempSelectedVendors = new Set(selectedVendors);
    tempVendorSortMode = vendorSortMode;
    
    // 更新UI显示
    updateFilterUI();
}

// 确认所有暂存的筛选条件
function confirmFilterConditions() {
    // 保存旧的排序模式用于比较
    const oldVendorSortMode = vendorSortMode;
    
    // 确认时间筛选
    timeFilterType = tempTimeFilterType;
    timeDateRange.start = tempTimeDateRange.start;
    timeDateRange.end = tempTimeDateRange.end;
    
    // 确认名称筛选
    nameFilter = tempNameFilter;
    
    // 确认人气筛选
    popularityFilter = tempPopularityFilter;
    
    // 确认标签筛选
    selectedTags = new Set(tempSelectedTags);
    tagFilterMode = tempTagFilterMode;
    
    // 确认厂商筛选
    selectedVendors = new Set(tempSelectedVendors);
    
    // 确认排序模式
    vendorSortMode = tempVendorSortMode;
    
    // 如果排序模式发生变化，需要重新排序应用列表
    if (oldVendorSortMode !== vendorSortMode) {
        // 重新排序应用列表
        sortApps();
    }
}</script>
</body>
</html>
